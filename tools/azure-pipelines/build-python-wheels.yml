# Licensed to the Apache Software Foundation (ASF) under one or more
# contributor license agreements.  See the NOTICE file distributed with
# this work for additional information regarding copyright ownership.
# The ASF licenses this file to You under the Apache License, Version 2.0
# (the "License"); you may not use this file except in compliance with
# the License.  You may obtain a copy of the License at
#
#    http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

jobs:
  - job: build_wheels_on_Linux
    pool:
      vmImage: 'ubuntu-20.04'
    steps:
      - script: |
          cd flink-python
          bash dev/build-wheels.sh
        displayName: Build wheels
      - task: PublishPipelineArtifact@0
        inputs:
          artifactName: 'wheel_$(Agent.OS)_$(Agent.JobName)'
          targetPath: 'flink-python/dist'

  - job: build_wheels_on_macos
    pool:
      vmImage: 'macOS-latest'
    steps:
      - task: UsePythonVersion@0
        inputs:
          versionSpec: '3.9'
      - script: |
          cd flink-python
          python -m pip install --upgrade pip
          pip install cibuildwheel==2.8.0
          cibuildwheel --platform macos --output-dir dist .
      - task: PublishPipelineArtifact@0
        inputs:
          artifactName: 'wheel_$(Agent.OS)_$(Agent.JobName)'
          targetPath: 'flink-python/dist'

