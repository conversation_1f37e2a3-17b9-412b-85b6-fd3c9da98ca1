<?xml version="1.0"?>
<!--
Licensed to the Apache Software Foundation (ASF) under one
or more contributor license agreements.  See the NOTICE file
distributed with this work for additional information
regarding copyright ownership.  The ASF licenses this file
to you under the Apache License, Version 2.0 (the
"License"); you may not use this file except in compliance
with the License.  You may obtain a copy of the License at

  http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing,
software distributed under the License is distributed on an
"AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
KIND, either express or implied.  See the License for the
specific language governing permissions and limitations
under the License.
-->

<!DOCTYPE suppressions PUBLIC
	"-//Puppy Crawl//DTD Suppressions 1.1//EN"
	"http://www.puppycrawl.com/dtds/suppressions_1_1.dtd">

<suppressions>
	<!-- These use star import for all the generated Tuple classes -->
	<suppress files="TupleTypeInfo.java" checks="AvoidStarImport"/>
	<!-- Explicitly tests for POJOs using underscores in field/parameter names -->
	<suppress files="PojoTypeInfoTest.java" checks="ParameterName|MemberName"/>
	<!-- Explicitly tests for POJOs using unicode in field names -->
	<suppress files="PojoTypeExtractionTest.java" checks="MemberName"/>
	<!-- Public API -->
	<suppress files="Either.java" checks="MethodNameCheck"/>
	<!-- Plain Old Code-->
	<suppress files="TypeExtractor.java" checks="ParenPad"/>
	<suppress files="Record.java" checks="LocalVariableName"/>
	<suppress files="CopyableValueTest.java" checks="LocalVariableName"/>
	<suppress files="GenericArraySerializer.java" checks="MemberName"/>
	<suppress files="PojoTypeInformationTest.java" checks="MemberName"/>
	<suppress files="FileInputFormatTest.java" checks="NeedBraces"/>
	<suppress files="AbstractGenericTypeComparatorTest.java" checks="NeedBraces"/>
	<suppress files="ParserTestBase.java" checks="NeedBraces"/>

	<!-- Legacy mockito usages -->
	<suppress files="MethodForwardingTestUtil.java|InitOutputPathTest.java|LimitedConnectionsFileSystemDelegationTest.java"
			  checks="IllegalImport"/>

	<!-- Legacy powermock usages -->
	<suppress files="InitOutputPathTest.java"
			  checks="IllegalImport"/>

	<suppress
		files="(.*)api[/\\]java[/\\]typeutils[/\\]runtime[/\\](.*)"
		checks="RedundantModifier|JavadocParagraph|JavadocType|JavadocStyle|StaticVariableNameCheck|LocalFinalVariableName|EmptyLineSeparator"/>
	<!--Only additional checks for test sources. Those checks were present in the "pre-strict" checkstyle but were not applied to test sources. We do not want to suppress them for sources directory-->
	<suppress
		files="(.*)test[/\\](.*)api[/\\]java[/\\]typeutils[/\\]runtime[/\\](.*)"
		checks="AvoidStarImport"/>

	<suppress
		files="(.*)api[/\\]java[/\\]typeutils[/\\]([^/\\]*\.java)"
		checks="RedundantModifier|JavadocParagraph|JavadocType|JavadocStyle|StaticVariableNameCheck|LocalFinalVariableName|EmptyLineSeparator"/>
	<!--Only additional checks for test sources. Those checks were present in the "pre-strict" checkstyle but were not applied to test sources. We do not want to suppress them for sources directory-->
	<suppress
		files="(.*)test[/\\](.*)api[/\\]java[/\\]typeutils[/\\]([^/\\]*\.java)"
		checks="AvoidStarImport|UnusedImports"/>

	<suppress
		files="(.*)api[/\\]common[/\\]accumulators[/\\](.*)"
		checks="RedundantModifier|JavadocParagraph|JavadocType|JavadocStyle|StaticVariableNameCheck|LocalFinalVariableName|EmptyLineSeparator"/>

	<suppress
		files="(.*)api[/\\]common[/\\]aggregators[/\\](.*)"
		checks="RedundantModifier|JavadocParagraph|JavadocType|JavadocStyle|StaticVariableNameCheck|LocalFinalVariableName|EmptyLineSeparator"/>

	<suppress
		files="(.*)api[/\\]common[/\\]io[/\\](.*)"
		checks="RedundantModifier|JavadocParagraph|JavadocType|JavadocStyle|StaticVariableNameCheck|LocalFinalVariableName|EmptyLineSeparator"/>
	<!--Only additional checks for test sources. Those checks were present in the "pre-strict" checkstyle but were not applied to test sources. We do not want to suppress them for sources directory-->

	<suppress
		files="(.*)api[/\\]common[/\\]operators[/\\](.*)"
		checks="RedundantModifier|JavadocParagraph|JavadocType|JavadocStyle|StaticVariableNameCheck|LocalFinalVariableName|EmptyLineSeparator"/>
	<!--Only additional checks for test sources. Those checks were present in the "pre-strict" checkstyle but were not applied to test sources. We do not want to suppress them for sources directory-->
	<suppress
		files="(.*)test[/\\](.*)api[/\\]common[/\\]operators[/\\](.*)"
		checks="AvoidStarImport"/>

	<suppress
		files="(.*)api[/\\]common[/\\]typeutils[/\\](.*)"
		checks="RedundantModifier|JavadocParagraph|JavadocType|JavadocStyle|StaticVariableNameCheck|LocalFinalVariableName|EmptyLineSeparator"/>
	<!--Only additional checks for test sources. Those checks were present in the "pre-strict" checkstyle but were not applied to test sources. We do not want to suppress them for sources directory-->
	<suppress
		files="(.*)test[/\\](.*)api[/\\]common[/\\]typeutils[/\\](.*)"
		checks="AvoidStarImport|ArrayTypeStyle|Regexp"/>

	<suppress
		files="(.*)api[/\\]common[/\\]distributions[/\\](.*)"
		checks="RedundantModifier|JavadocParagraph|JavadocType|JavadocStyle|StaticVariableNameCheck|LocalFinalVariableName|EmptyLineSeparator"/>

	<suppress
		files="(.*)api[/\\]common[/\\]([^/\\]*\.java)"
		checks="RedundantModifier|JavadocParagraph|JavadocType|JavadocStyle|StaticVariableNameCheck|LocalFinalVariableName|EmptyLineSeparator"/>

	<suppress
		files="(.*)core[/\\]io[/\\](.*)"
		checks="RedundantModifier|JavadocParagraph|JavadocType|JavadocStyle|StaticVariableNameCheck|LocalFinalVariableName|EmptyLineSeparator"/>
	<!--Only additional checks for test sources. Those checks were present in the "pre-strict" checkstyle but were not applied to test sources. We do not want to suppress them for sources directory-->
	<suppress
		files="(.*)test[/\\](.*)core[/\\]io[/\\](.*)"
		checks="AvoidStarImport"/>

	<suppress
		files="(.*)types[/\\](.*)"
		checks="RedundantModifier|JavadocParagraph|JavadocType|JavadocStyle|StaticVariableNameCheck|LocalFinalVariableName|EmptyLineSeparator"/>
	<!--Only additional checks for test sources. Those checks were present in the "pre-strict" checkstyle but were not applied to test sources. We do not want to suppress them for sources directory-->
	<suppress
		files="(.*)test[/\\](.*)types[/\\](.*)"
		checks="AvoidStarImport"/>

	<suppress
		files="(.*)test[/\\](.*)testutils[/\\](.*)"
		checks="RedundantModifier|JavadocParagraph|JavadocType|JavadocStyle|StaticVariableNameCheck|LocalFinalVariableName|EmptyLineSeparator"/>
</suppressions>
