/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.yarn;

import org.apache.hadoop.yarn.client.api.AMRMClient;
import org.apache.hadoop.yarn.client.api.async.AMRMClientAsync;

/** Default implementation of {@link YarnResourceManagerClientFactory}. */
public class DefaultYarnResourceManagerClientFactory implements YarnResourceManagerClientFactory {

    private static final YarnResourceManagerClientFactory INSTANCE =
            new DefaultYarnResourceManagerClientFactory();

    private DefaultYarnResourceManagerClientFactory() {}

    public static YarnResourceManagerClientFactory getInstance() {
        return INSTANCE;
    }

    @Override
    public AMRMClientAsync<AMRMClient.ContainerRequest> createResourceManagerClient(
            int yarnHeartbeatIntervalMillis, AMRMClientAsync.CallbackHandler callbackHandler) {
        return AMRMClientAsync.createAMRMClientAsync(yarnHeartbeatIntervalMillis, callbackHandler);
    }
}
