## Refactoring `flink-checkpoint-inspector` to use <PERSON><PERSON><PERSON><PERSON>

This document outlines the tasks required to refactor the `flink-checkpoint-inspector` submodule from using Apache Commons CLI to <PERSON><PERSON><PERSON><PERSON> for command-line argument parsing. Each task is designed to be self-contained and actionable.

---

### Task 1: Update Maven Dependencies and Build Configuration

**Objective:** Replace `commons-cli` with `picocli` and configure <PERSON><PERSON> for annotation processing.

**Action:**
1.  **Modify `pom.xml`**:
    *   Remove the `commons-cli` dependency.
    *   Add the `picocli` dependency (groupId: `info.picocli`, artifactId: `picocli`, version: `4.7.5`).
    *   Add a `maven-compiler-plugin` configuration to enable annotation processing for `picocli-codegen` (groupId: `info.picocli`, artifactId: `picocli-codegen`, version: `4.7.5`). Ensure `annotationProcessorPaths` and `compilerArgs` are correctly set up for <PERSON><PERSON><PERSON><PERSON>.

---

### Task 2: Create `CheckpointMetadataUtils` Class

**Objective:** Centralize common utility methods related to checkpoint metadata.

**Action:**
1.  **Create new file**: `src/main/java/org/apache/flink/tools/checkpoint/utils/CheckpointMetadataUtils.java`.
2.  **Add methods**:
    *   `public static CheckpointMetadata loadMetadata(String checkpointPath) throws IOException`: Encapsulate `SavepointLoader.loadSavepointMetadata`.
    *   `public static boolean hasKeyedState(OperatorState operatorState)`: Check if an operator has any keyed state.
    *   `public static boolean hasOperatorState(OperatorState operatorState)`: Check if an operator has any operator state.
    *   `public static List<OperatorState> getOperatorsWithKeyedState(CheckpointMetadata metadata)`: Filter operators with keyed state.
    *   `public static List<OperatorState> getOperatorsWithOperatorState(CheckpointMetadata metadata)`: Filter operators with operator state.
    *   `public static String formatBytes(long bytes)`: Utility for formatting byte sizes into human-readable strings (KB, MB, GB).
    *   `public static class CheckpointSummary`: A simple data class to hold summary statistics for a checkpoint.

---

### Task 3: Refactor Base Command Interface (`Command.java`)

**Objective:** Adapt the `Command` interface to Picocli's `Callable` pattern.

**Action:**
1.  **Modify `src/main/java/org/apache/flink/tools/checkpoint/commands/Command.java`**:
    *   Change the interface to extend `java.util.concurrent.Callable<Integer>`.
    *   Remove the `void execute(String[] args) throws Exception` method.
    *   Add the `Integer call() throws Exception` method, which is required by `Callable`.

---

### Task 4: Refactor Main Application Class (`FlinkCheckpointInspector.java`)

**Objective:** Transform the main class into a Picocli command runner.

**Action:**
1.  **Modify `src/main/java/org/apache/flink/tools/checkpoint/FlinkCheckpointInspector.java`**:
    *   Annotate the class with `@CommandLine.Command` to define it as the main command.
        *   Set `name = "flink-checkpoint-inspector"`.
        *   List all command classes (e.g., `MetadataCommand.class`, `OperatorsCommand.class`, etc.) in the `subcommands` attribute.
        *   Add `CommandLine.HelpCommand.class` to `subcommands` for auto-generated help.
        *   Add a `description` and `mixinStandardHelpOptions = true`, `version` attributes.
    *   Implement the `Runnable` interface (or `Callable<Integer>`). The `run()` method can be empty or print a default message, as Picocli will show help if no subcommand is given.
    *   Update the `main` method:
        *   Remove all manual argument parsing and command dispatching logic (the `if (args.length == 0)` check and the `switch` statement).
        *   Use `int exitCode = new CommandLine(new FlinkCheckpointInspector()).execute(args);` to run the Picocli command line.
        *   Call `System.exit(exitCode);`.
    *   Update `loadMetadata(String checkpointPath)`:
        *   Change its implementation to call `CheckpointMetadataUtils.loadMetadata(checkpointPath)`.
    *   Remove the `printUsage()` method.

---

### Task 5: Refactor `MetadataCommand`

**Objective:** Convert `MetadataCommand` into a Picocli subcommand.

**Action:**
1.  **Modify `src/main/java/org/apache/flink/tools/checkpoint/commands/MetadataCommand.java`**:
    *   Annotate the class with `@CommandLine.Command(name = "metadata", description = "Show checkpoint metadata")`.
    *   Replace the manual argument parsing (`if (args.length != 1)`) with `@CommandLine.Parameters(index = "0", description = "The path to the checkpoint.")` for the `checkpointPath` field.
    *   Change the `execute` method to `public Integer call() throws Exception`.
    *   Inside `call()`, load metadata using `FlinkCheckpointInspector.loadMetadata(checkpointPath)`.
    *   Ensure `printMetadata` uses `CheckpointMetadataUtils.formatBytes` for byte formatting.
    *   Return `0` from `call()` on successful execution.

---

### Task 6: Refactor `OperatorsCommand`

**Objective:** Convert `OperatorsCommand` into a Picocli subcommand.

**Action:**
1.  **Modify `src/main/java/org/apache/flink/tools/checkpoint/commands/OperatorsCommand.java`**:
    *   Annotate the class with `@CommandLine.Command(name = "operators", description = "List all operators and their state")`.
    *   Replace the manual argument parsing (`if (args.length != 1)`) with `@CommandLine.Parameters(index = "0", description = "The path to the checkpoint.")` for the `checkpointPath` field.
    *   Change the `execute` method to `public Integer call() throws Exception`.
    *   Inside `call()`, load metadata using `FlinkCheckpointInspector.loadMetadata(checkpointPath)`.
    *   Ensure `listOperators` uses `CheckpointMetadataUtils.formatBytes` for byte formatting.
    *   Return `0` from `call()` on successful execution.

---

### Task 7: Refactor `KeyedStateCommand`

**Objective:** Convert `KeyedStateCommand` into a Picocli subcommand.

**Action:**
1.  **Modify `src/main/java/org/apache/flink/tools/checkpoint/commands/KeyedStateCommand.java`**:
    *   Annotate the class with `@CommandLine.Command(name = "keyed-state", description = "Inspect keyed state for specific operator")`.
    *   Remove all `Options`, `CommandLineParser`, `DefaultParser`, `HelpFormatter`, and `ParseException` related code.
    *   Replace argument parsing with Picocli annotations:
        *   `@CommandLine.Parameters(index = "0", description = "The path to the checkpoint.")` for `checkpointPath`.
        *   `@CommandLine.Option(names = {"-o", "--operator-id"}, required = true, description = "Operator ID")` for `operatorId`.
        *   `@CommandLine.Option(names = {"-s", "--subtask"}, description = "Subtask index (default: 0)", defaultValue = "0")` for `subtaskIndex`.
        *   `@CommandLine.Option(names = {"-v", "--verbose"}, description = "Verbose output")` for `verbose`.
    *   Change the `execute` method to `public Integer call() throws Exception`.
    *   Inside `call()`, load metadata using `FlinkCheckpointInspector.loadMetadata(checkpointPath)`.
    *   If operator not found, print error and return `1`. Otherwise, return `0`.
    *   Ensure `inspectKeyedStateHandle` uses `CheckpointMetadataUtils.formatBytes` for byte formatting.

---

### Task 8: Refactor `OperatorStateCommand`

**Objective:** Convert `OperatorStateCommand` into a Picocli subcommand.

**Action:**
1.  **Modify `src/main/java/org/apache/flink/tools/checkpoint/commands/OperatorStateCommand.java`**:
    *   Annotate the class with `@CommandLine.Command(name = "operator-state", description = "Inspect operator state for specific operator")`.
    *   Remove all `Options`, `CommandLineParser`, `DefaultParser`, `HelpFormatter`, and `ParseException` related code.
    *   Replace argument parsing with Picocli annotations:
        *   `@CommandLine.Parameters(index = "0", description = "The path to the checkpoint.")` for `checkpointPath`.
        *   `@CommandLine.Option(names = {"-o", "--operator-id"}, required = true, description = "Operator ID")` for `operatorId`.
        *   `@CommandLine.Option(names = {"-s", "--subtask"}, description = "Subtask index (default: 0)", defaultValue = "0")` for `subtaskIndex`.
        *   `@CommandLine.Option(names = {"-v", "--verbose"}, description = "Verbose output")` for `verbose`.
    *   Change the `execute` method to `public Integer call() throws Exception`.
    *   Inside `call()`, load metadata using `FlinkCheckpointInspector.loadMetadata(checkpointPath)`.
    *   If operator not found, print error and return `1`. Otherwise, return `0`.
    *   Ensure `inspectOperatorStateHandle` uses `CheckpointMetadataUtils.formatBytes` for byte formatting.

---

### Task 9: Refactor `DumpStateCommand`

**Objective:** Convert `DumpStateCommand` into a Picocli subcommand.

**Action:**
1.  **Modify `src/main/java/org/apache/flink/tools/checkpoint/commands/DumpStateCommand.java`**:
    *   Annotate the class with `@CommandLine.Command(name = "dump-state", description = "Dump state data to a file or stdout.")`.
    *   Remove all `Options`, `CommandLineParser`, `DefaultParser`, `HelpFormatter`, and `ParseException` related code.
    *   Replace argument parsing with Picocli annotations:
        *   `@CommandLine.Parameters(index = "0", description = "The path to the checkpoint.")` for `checkpointPath`.
        *   `@CommandLine.Option(names = {"-o", "--operator-id"}, required = true, description = "Operator ID")` for `operatorId`.
        *   `@CommandLine.Option(names = {"-s", "--subtask"}, description = "Subtask index (default: 0)", defaultValue = "0")` for `subtaskIndex`.
        *   `@CommandLine.Option(names = {"-n", "--state-name"}, description = "State name to dump")` for `stateName`.
        *   `@CommandLine.Option(names = {"-f", "--output"}, description = "Output file (default: stdout)")` for `outputFile`.
        *   `@CommandLine.Option(names = {"--format"}, description = "Output format: json or csv (default: json)", defaultValue = "json")` for `format`.
        *   `@CommandLine.Option(names = {"--limit"}, description = "Limit number of records (default: no limit)", defaultValue = "-1")` for `limit`.
    *   Change the `execute` method to `public Integer call() throws Exception`.
    *   Inside `call()`, load metadata using `FlinkCheckpointInspector.loadMetadata(checkpointPath)`.
    *   If operator not found, print error and return `1`.
    *   If unsupported format, print error and return `1`. Otherwise, return `0`.

---

### Task 10: Refactor `KeyGroupsCommand`

**Objective:** Convert `KeyGroupsCommand` into a Picocli subcommand.

**Action:**
1.  **Modify `src/main/java/org/apache/flink/tools/checkpoint/commands/KeyGroupsCommand.java`**:
    *   Annotate the class with `@CommandLine.Command(name = "key-groups", description = "Show key group distribution across operators and subtasks")`.
    *   Replace the manual argument parsing (`if (args.length != 1)`) with `@CommandLine.Parameters(index = "0", description = "The path to the checkpoint.")` for the `checkpointPath` field.
    *   Change the `execute` method to `public Integer call() throws Exception`.
    *   Inside `call()`, load metadata using `FlinkCheckpointInspector.loadMetadata(checkpointPath)`.
    *   Ensure `analyzeKeyGroups` uses `CheckpointMetadataUtils.formatBytes` for byte formatting.
    *   Return `0` from `call()` on successful execution.

---

### Task 11: Update `README.md`

**Objective:** Reflect the new Picocli-based command-line usage and dependency changes in the documentation.

**Action:**
1.  **Modify `README.md`**:
    *   Update the "Basic Syntax" section to reflect the Picocli command structure (e.g., `./bin/flink-checkpoint-inspector.sh <command> <checkpoint-path> [options]`).
    *   Update all command examples to use the new syntax.
    *   In the "Dependencies" section, change "Apache Commons CLI for command-line parsing" to "Picocli for command-line parsing".
