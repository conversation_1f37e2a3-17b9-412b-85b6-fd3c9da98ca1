/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you in the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.tools.checkpoint;

import org.apache.flink.runtime.checkpoint.OperatorState;
import org.apache.flink.runtime.checkpoint.OperatorSubtaskState;
import org.apache.flink.runtime.checkpoint.metadata.CheckpointMetadata;
import org.apache.flink.runtime.state.KeyedStateHandle;
import org.apache.flink.runtime.state.OperatorStateHandle;
import org.apache.flink.tools.checkpoint.commands.Command;
import org.apache.flink.tools.checkpoint.commands.KeyGroupsCommand;
import org.apache.flink.tools.checkpoint.commands.KeyedStateCommand;
import org.apache.flink.tools.checkpoint.commands.MetadataCommand;
import org.apache.flink.tools.checkpoint.commands.OperatorStateCommand;
import org.apache.flink.tools.checkpoint.commands.OperatorsCommand;
import org.apache.flink.tools.checkpoint.commands.S3HelpCommand;
import org.apache.flink.tools.checkpoint.utils.CheckpointMetadataUtils;
import org.apache.flink.tools.checkpoint.utils.S3Configuration;
import picocli.CommandLine;

import java.io.IOException;
import java.util.concurrent.Callable;

@CommandLine.Command(
        name = "flink-checkpoint-inspector",
        description = "A tool for inspecting Flink checkpoints and savepoints.",
        subcommands = {
            MetadataCommand.class,
            OperatorsCommand.class,
            KeyedStateCommand.class,
            OperatorStateCommand.class,
            KeyGroupsCommand.class,
            S3HelpCommand.class,
            CommandLine.HelpCommand.class
        },
        mixinStandardHelpOptions = true,
        version = "1.0")
public class FlinkCheckpointInspector implements Callable<Integer> {

    public static void main(String[] args) {
        int exitCode = new CommandLine(new FlinkCheckpointInspector()).execute(args);
        System.exit(exitCode);
    }

    @Override
    public Integer call() {
        // This is executed when no subcommand is specified.
        // Picocli will automatically print the help message.
        return 0;
    }

    /** Load checkpoint metadata from the given path. */
    public static CheckpointMetadata loadMetadata(String checkpointPath) throws IOException {
        // Initialize S3 configuration if needed
        if (S3Configuration.isS3Path(checkpointPath)) {
            S3Configuration.initialize();
        }
        return CheckpointMetadataUtils.loadMetadata(checkpointPath);
    }

    /** Find operator state by operator ID. */
    public static OperatorState findOperatorById(CheckpointMetadata metadata, String operatorId) {
        return metadata.getOperatorStates().stream()
                .filter(
                        op ->
                                op.getOperatorID().toString().equals(operatorId)
                                        || op.getOperatorID().toHexString().equals(operatorId))
                .findFirst()
                .orElse(null);
    }

    /** Print basic information about a subtask state. */
    public static void printSubtaskStateInfo(
            int subtaskIndex, OperatorSubtaskState subtaskState) {
        System.out.println("    Subtask " + subtaskIndex + ":");

        // Managed keyed state
        if (!subtaskState.getManagedKeyedState().isEmpty()) {
            System.out.println("      Managed Keyed State:");
            for (KeyedStateHandle handle : subtaskState.getManagedKeyedState()) {
                System.out.println(
                        "        - "
                                + handle.getClass().getSimpleName()
                                + " (size: "
                                + CheckpointMetadataUtils.formatBytes(handle.getStateSize())
                                + ", key groups: "
                                + handle.getKeyGroupRange()
                                + ")");
            }
        }

        // Raw keyed state
        if (!subtaskState.getRawKeyedState().isEmpty()) {
            System.out.println("      Raw Keyed State:");
            for (KeyedStateHandle handle : subtaskState.getRawKeyedState()) {
                System.out.println(
                        "        - "
                                + handle.getClass().getSimpleName()
                                + " (size: "
                                + CheckpointMetadataUtils.formatBytes(handle.getStateSize())
                                + ", key groups: "
                                + handle.getKeyGroupRange()
                                + ")");
            }
        }

        // Managed operator state
        if (!subtaskState.getManagedOperatorState().isEmpty()) {
            System.out.println("      Managed Operator State:");
            for (OperatorStateHandle handle : subtaskState.getManagedOperatorState()) {
                System.out.println(
                        "        - "
                                + handle.getClass().getSimpleName()
                                + " (size: "
                                + CheckpointMetadataUtils.formatBytes(handle.getStateSize())
                                + ")");
                if (handle.getStateNameToPartitionOffsets() != null) {
                    for (String stateName : handle.getStateNameToPartitionOffsets().keySet()) {
                        System.out.println("          State: " + stateName);
                    }
                }
            }
        }

        // Raw operator state
        if (!subtaskState.getRawOperatorState().isEmpty()) {
            System.out.println("      Raw Operator State:");
            for (OperatorStateHandle handle : subtaskState.getRawOperatorState()) {
                System.out.println(
                        "        - "
                                + handle.getClass().getSimpleName()
                                + " (size: "
                                + CheckpointMetadataUtils.formatBytes(handle.getStateSize())
                                + ")");
            }
        }
    }
}
