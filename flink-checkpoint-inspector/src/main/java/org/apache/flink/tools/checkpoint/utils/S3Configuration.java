/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.tools.checkpoint.utils;

import org.apache.flink.configuration.Configuration;
import org.apache.flink.core.fs.FileSystem;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Utility class for configuring S3 filesystem access.
 * 
 * <p>This class handles the initialization of S3 filesystem configuration
 * for accessing checkpoints stored in S3. It supports both AWS credentials
 * from environment variables and IAM roles.
 */
public class S3Configuration {
    
    private static final Logger LOG = LoggerFactory.getLogger(S3Configuration.class);
    
    private static boolean initialized = false;
    
    /**
     * Initialize S3 filesystem configuration.
     * 
     * <p>This method sets up the necessary configuration for accessing S3
     * using Flink's filesystem abstraction. It reads AWS credentials from
     * environment variables if available.
     */
    public static synchronized void initialize() {
        if (initialized) {
            return;
        }
        
        Configuration config = new Configuration();
        
        // Configure S3 filesystem
        config.setString("s3.access-key", getEnvOrDefault("AWS_ACCESS_KEY_ID", ""));
        config.setString("s3.secret-key", getEnvOrDefault("AWS_SECRET_ACCESS_KEY", ""));
        
        // Optional: Configure S3 endpoint for non-AWS S3-compatible storage
        String s3Endpoint = getEnvOrDefault("S3_ENDPOINT", "");
        if (!s3Endpoint.isEmpty()) {
            config.setString("s3.endpoint", s3Endpoint);
        }
        
        // Optional: Configure S3 region
        String s3Region = getEnvOrDefault("AWS_DEFAULT_REGION", "us-east-1");
        config.setString("s3.region", s3Region);
        
        // Configure path style access (useful for MinIO and other S3-compatible storage)
        String pathStyleAccess = getEnvOrDefault("S3_PATH_STYLE_ACCESS", "false");
        config.setString("s3.path.style.access", pathStyleAccess);
        
        try {
            // Initialize the filesystem with the configuration
            FileSystem.initialize(config, null);
            initialized = true;
            LOG.info("S3 filesystem configuration initialized successfully");
        } catch (Exception e) {
            LOG.warn("Failed to initialize S3 filesystem configuration: {}", e.getMessage());
            // Continue without S3 configuration - might still work with default AWS credentials
        }
    }
    
    /**
     * Check if the given path is an S3 path.
     * 
     * @param path The path to check
     * @return true if the path starts with s3:// or s3a://
     */
    public static boolean isS3Path(String path) {
        return path.startsWith("s3://") || path.startsWith("s3a://");
    }
    
    /**
     * Get environment variable value or return default.
     * 
     * @param envVar Environment variable name
     * @param defaultValue Default value if environment variable is not set
     * @return Environment variable value or default
     */
    private static String getEnvOrDefault(String envVar, String defaultValue) {
        String value = System.getenv(envVar);
        return value != null ? value : defaultValue;
    }
    
    /**
     * Print S3 configuration help.
     */
    public static void printS3Help() {
        System.out.println("S3 Configuration:");
        System.out.println("  The tool supports reading checkpoints from S3. Configure access using:");
        System.out.println("  - AWS_ACCESS_KEY_ID: Your AWS access key");
        System.out.println("  - AWS_SECRET_ACCESS_KEY: Your AWS secret key");
        System.out.println("  - AWS_DEFAULT_REGION: AWS region (default: us-east-1)");
        System.out.println("  - S3_ENDPOINT: Custom S3 endpoint (for non-AWS S3-compatible storage)");
        System.out.println("  - S3_PATH_STYLE_ACCESS: Use path-style access (true/false, default: false)");
        System.out.println();
        System.out.println("  Alternatively, use IAM roles or AWS credential profiles.");
        System.out.println();
        System.out.println("  Example S3 paths:");
        System.out.println("    s3://my-bucket/checkpoints/checkpoint-123/");
        System.out.println("    s3a://my-bucket/checkpoints/checkpoint-123/_metadata");
        System.out.println();
    }
}
