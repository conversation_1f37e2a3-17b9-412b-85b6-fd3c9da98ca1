/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.tools.checkpoint.commands;

import org.apache.flink.runtime.checkpoint.OperatorState;
import org.apache.flink.runtime.checkpoint.OperatorSubtaskState;
import org.apache.flink.runtime.checkpoint.metadata.CheckpointMetadata;
import org.apache.flink.runtime.state.KeyGroupsStateHandle;
import org.apache.flink.runtime.state.KeyedStateHandle;
import org.apache.flink.tools.checkpoint.FlinkCheckpointInspector;
import org.apache.flink.tools.checkpoint.utils.StateDataReader;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import picocli.CommandLine;

import java.io.FileWriter;
import java.io.PrintWriter;
import java.util.Map;

/**
 * Command to dump state data to a file.
 */
@CommandLine.Command(name = "dump-state", description = "Dump state data to a file or stdout.")
public class DumpStateCommand implements Command {

    @CommandLine.Parameters(index = "0", description = "The path to the checkpoint.")
    private String checkpointPath;

    @CommandLine.Option(names = {"-o", "--operator-id"}, required = true, description = "Operator ID")
    private String operatorId;

    @CommandLine.Option(names = {"-s", "--subtask"}, description = "Subtask index (default: 0)", defaultValue = "0")
    private int subtaskIndex;

    @CommandLine.Option(names = {"-n", "--state-name"}, description = "State name to dump")
    private String stateName;

    @CommandLine.Option(names = {"-f", "--output"}, description = "Output file (default: stdout)")
    private String outputFile;

    @CommandLine.Option(names = {"--format"}, description = "Output format: json or csv (default: json)", defaultValue = "json")
    private String format;

    @CommandLine.Option(names = {"--limit"}, description = "Limit number of records (default: no limit)", defaultValue = "-1")
    private int limit;

    @Override
    public Integer call() throws Exception {
        CheckpointMetadata metadata = FlinkCheckpointInspector.loadMetadata(checkpointPath);
        OperatorState operatorState = FlinkCheckpointInspector.findOperatorById(metadata, operatorId);

        if (operatorState == null) {
            System.err.println("Operator not found: " + operatorId);
            return 1;
        }

        if (!"json".equalsIgnoreCase(format) && !"csv".equalsIgnoreCase(format)) {
            System.err.println("Unsupported format: " + format);
            return 1;
        }

        dumpState(operatorState, subtaskIndex, stateName, outputFile, format, limit);
        return 0;
    }

    private void dumpState(OperatorState operatorState, int subtaskIndex, String stateName, 
                          String outputFile, String format, int limit) throws Exception {
        
        System.out.println("=== Dumping State Data ===");
        System.out.println("Operator ID: " + operatorState.getOperatorID());
        System.out.println("Subtask: " + subtaskIndex);
        System.out.println("State Name: " + (stateName != null ? stateName : "ALL"));
        System.out.println("Format: " + format);
        System.out.println("Output: " + (outputFile != null ? outputFile : "stdout"));
        System.out.println();

        OperatorSubtaskState subtaskState = operatorState.getSubtaskStates().get(subtaskIndex);
        if (subtaskState == null) {
            System.err.println("Subtask not found: " + subtaskIndex);
            return;
        }

        PrintWriter writer = outputFile != null ? 
            new PrintWriter(new FileWriter(outputFile)) : 
            new PrintWriter(System.out);

        try {
            if ("json".equalsIgnoreCase(format)) {
                dumpStateAsJson(subtaskState, stateName, writer, limit);
            } else if ("csv".equalsIgnoreCase(format)) {
                dumpStateAsCsv(subtaskState, stateName, writer, limit);
            } else {
                System.err.println("Unsupported format: " + format);
                System.exit(1);
            }
        } finally {
            if (outputFile != null) {
                writer.close();
            }
        }

        if (outputFile != null) {
            System.out.println("State data written to: " + outputFile);
        }
    }

    private void dumpStateAsJson(OperatorSubtaskState subtaskState, String stateName, 
                                PrintWriter writer, int limit) throws Exception {
        ObjectMapper mapper = new ObjectMapper();
        ObjectNode root = mapper.createObjectNode();
        ArrayNode stateArray = mapper.createArrayNode();

        int recordCount = 0;
        
        // Process managed keyed state
        for (KeyedStateHandle handle : subtaskState.getManagedKeyedState()) {
            if (handle instanceof KeyGroupsStateHandle) {
                KeyGroupsStateHandle keyGroupsHandle = (KeyGroupsStateHandle) handle;
                
                try {
                    StateDataReader reader = new StateDataReader(keyGroupsHandle);
                    Map<String, Object> stateData = reader.readStateData(stateName);
                    
                    for (Map.Entry<String, Object> entry : stateData.entrySet()) {
                        if (limit > 0 && recordCount >= limit) {
                            break;
                        }
                        
                        ObjectNode record = mapper.createObjectNode();
                        record.put("key", entry.getKey());
                        record.put("value", entry.getValue().toString());
                        record.put("state_name", stateName != null ? stateName : "unknown");
                        stateArray.add(record);
                        recordCount++;
                    }
                } catch (Exception e) {
                    ObjectNode errorRecord = mapper.createObjectNode();
                    errorRecord.put("error", "Failed to read state: " + e.getMessage());
                    stateArray.add(errorRecord);
                }
                
                if (limit > 0 && recordCount >= limit) {
                    break;
                }
            }
        }

        root.set("state_data", stateArray);
        root.put("total_records", recordCount);
        root.put("limited", limit > 0 && recordCount >= limit);

        writer.println(mapper.writerWithDefaultPrettyPrinter().writeValueAsString(root));
    }

    private void dumpStateAsCsv(OperatorSubtaskState subtaskState, String stateName, 
                               PrintWriter writer, int limit) throws Exception {
        // CSV header
        writer.println("state_name,key,value");

        int recordCount = 0;
        
        // Process managed keyed state
        for (KeyedStateHandle handle : subtaskState.getManagedKeyedState()) {
            if (handle instanceof KeyGroupsStateHandle) {
                KeyGroupsStateHandle keyGroupsHandle = (KeyGroupsStateHandle) handle;
                
                try {
                    StateDataReader reader = new StateDataReader(keyGroupsHandle);
                    Map<String, Object> stateData = reader.readStateData(stateName);
                    
                    for (Map.Entry<String, Object> entry : stateData.entrySet()) {
                        if (limit > 0 && recordCount >= limit) {
                            break;
                        }
                        
                        String escapedKey = escapeCsvValue(entry.getKey());
                        String escapedValue = escapeCsvValue(entry.getValue().toString());
                        String escapedStateName = escapeCsvValue(stateName != null ? stateName : "unknown");
                        
                        writer.println(escapedStateName + "," + escapedKey + "," + escapedValue);
                        recordCount++;
                    }
                } catch (Exception e) {
                    writer.println("ERROR,ERROR,\"Failed to read state: " + e.getMessage() + "\"");
                }
                
                if (limit > 0 && recordCount >= limit) {
                    break;
                }
            }
        }

        System.err.println("Total records written: " + recordCount);
    }

    private String escapeCsvValue(String value) {
        if (value == null) {
            return "";
        }
        
        // Escape quotes and wrap in quotes if necessary
        if (value.contains(",") || value.contains("\"") || value.contains("\n")) {
            return "\"" + value.replace("\"", "\"\"") + "\"";
        }
        
        return value;
    }

    
}
