/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.tools.checkpoint.utils;

import org.apache.flink.core.fs.FSDataInputStream;
import org.apache.flink.core.memory.DataInputViewStreamWrapper;
import org.apache.flink.runtime.state.KeyGroupRange;
import org.apache.flink.runtime.state.KeyGroupsStateHandle;
import org.apache.flink.runtime.state.KeyedBackendSerializationProxy;
import org.apache.flink.runtime.state.heap.StateTableByKeyGroupReaders;
import org.apache.flink.runtime.state.metainfo.StateMetaInfoSnapshot;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Utility class for reading state data from KeyGroupsStateHandle.
 * 
 * <p>This class provides methods to extract and deserialize state data
 * from Flink checkpoint files. It handles the low-level details of
 * reading the binary format and provides a higher-level interface
 * for accessing state values.
 */
public class StateDataReader {
    
    private static final Logger LOG = LoggerFactory.getLogger(StateDataReader.class);
    
    private final KeyGroupsStateHandle stateHandle;
    private KeyedBackendSerializationProxy<?> serializationProxy;
    private List<StateMetaInfoSnapshot> stateMetaInfos;
    
    public StateDataReader(KeyGroupsStateHandle stateHandle) {
        this.stateHandle = stateHandle;
    }

    /**
     * Read state metadata from the handle.
     */
    public void readMetadata() throws IOException {
        try (FSDataInputStream inputStream = stateHandle.openInputStream()) {
            DataInputViewStreamWrapper inView = new DataInputViewStreamWrapper(inputStream);
            
            serializationProxy = new KeyedBackendSerializationProxy<>(getClass().getClassLoader());
            serializationProxy.read(inView);
            
            stateMetaInfos = serializationProxy.getStateMetaInfoSnapshots();
            
            LOG.info("Read metadata for {} states from handle", stateMetaInfos.size());
        }
    }

    /**
     * Get the list of state names available in this handle.
     */
    public List<String> getStateNames() throws IOException {
        if (stateMetaInfos == null) {
            readMetadata();
        }
        
        return stateMetaInfos.stream()
            .map(StateMetaInfoSnapshot::getName)
            .toList();
    }

    /**
     * Read state data for a specific state name.
     * 
     * @param stateName the name of the state to read, or null to read all states
     * @return a map of key-value pairs from the state
     */
    public Map<String, Object> readStateData(String stateName) throws Exception {
        if (stateMetaInfos == null) {
            readMetadata();
        }

        Map<String, Object> result = new HashMap<>();
        
        try (FSDataInputStream inputStream = stateHandle.openInputStream()) {
            DataInputViewStreamWrapper inView = new DataInputViewStreamWrapper(inputStream);
            
            // Skip the serialization proxy (already read)
            serializationProxy = new KeyedBackendSerializationProxy<>(getClass().getClassLoader());
            serializationProxy.read(inView);
            
            KeyGroupRange keyGroupRange = stateHandle.getKeyGroupRange();
            
            // Read state data for each key group
            for (int keyGroup : keyGroupRange) {
                long offset = stateHandle.getOffsetForKeyGroup(keyGroup);
                inputStream.seek(offset);
                
                try {
                    Map<String, Object> keyGroupData = readKeyGroupData(inView, stateName);
                    result.putAll(keyGroupData);
                } catch (Exception e) {
                    LOG.warn("Failed to read data for key group {}: {}", keyGroup, e.getMessage());
                    // Continue with other key groups
                }
            }
            
        } catch (Exception e) {
            LOG.error("Error reading state data", e);
            throw e;
        }
        
        return result;
    }

    /**
     * Read state data for a specific key group.
     */
    private Map<String, Object> readKeyGroupData(DataInputViewStreamWrapper inView, String targetStateName) throws Exception {
        Map<String, Object> result = new HashMap<>();
        
        // This is a simplified implementation
        // In practice, you would need to use the appropriate StateTableByKeyGroupReaders
        // based on the state backend version and configuration
        
        try {
            // Use the state table readers to deserialize the data
            // Note: This is a simplified implementation for demonstration
            // In practice, you would need to create a proper StateTable instance
            // StateSnapshotKeyGroupReader reader = StateTableByKeyGroupReaders.readerForVersion(stateTable, serializationProxy.getReadVersion());
            
            // Note: This is a placeholder implementation
            // The actual implementation would depend on the specific state backend format
            // and would require proper type serializers
            
            for (StateMetaInfoSnapshot metaInfo : stateMetaInfos) {
                String stateName = metaInfo.getName();
                
                if (targetStateName != null && !targetStateName.equals(stateName)) {
                    continue;
                }
                
                // Read state entries for this state
                // This is where you would use the actual state table readers
                // to deserialize the key-value pairs
                
                // For demonstration purposes, we'll add a placeholder entry
                result.put("state_" + stateName + "_placeholder", "Unable to deserialize - requires type serializers");
            }
            
        } catch (Exception e) {
            LOG.warn("Failed to read key group data: {}", e.getMessage());
            result.put("error", "Failed to deserialize state data: " + e.getMessage());
        }
        
        return result;
    }

    /**
     * Get information about the state handle.
     */
    public StateHandleInfo getStateHandleInfo() throws IOException {
        if (stateMetaInfos == null) {
            readMetadata();
        }
        
        StateHandleInfo info = new StateHandleInfo();
        info.stateSize = stateHandle.getStateSize();
        info.keyGroupRange = stateHandle.getKeyGroupRange();
        info.stateCount = stateMetaInfos.size();
        info.compressionEnabled = serializationProxy.isUsingKeyGroupCompression();
        info.stateNames = getStateNames();
        
        return info;
    }

    /**
     * Information about a state handle.
     */
    public static class StateHandleInfo {
        public long stateSize;
        public KeyGroupRange keyGroupRange;
        public int stateCount;
        public boolean compressionEnabled;
        public List<String> stateNames;
        
        @Override
        public String toString() {
            return String.format(
                "StateHandleInfo{size=%d, keyGroups=%s, states=%d, compressed=%s, names=%s}",
                stateSize, keyGroupRange, stateCount, compressionEnabled, stateNames
            );
        }
    }
}
