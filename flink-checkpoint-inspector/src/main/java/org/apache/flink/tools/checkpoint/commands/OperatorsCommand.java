/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.tools.checkpoint.commands;

import org.apache.flink.runtime.checkpoint.OperatorState;
import org.apache.flink.runtime.checkpoint.OperatorSubtaskState;
import org.apache.flink.runtime.checkpoint.metadata.CheckpointMetadata;
import org.apache.flink.tools.checkpoint.FlinkCheckpointInspector;
import org.apache.flink.tools.checkpoint.utils.CheckpointMetadataUtils;
import picocli.CommandLine;

import java.util.Map;

@CommandLine.Command(name = "operators", description = "List all operators and their state")
public class OperatorsCommand implements Command {

    @CommandLine.Parameters(index = "0", description = "The path to the checkpoint.")
    private String checkpointPath;

    @Override
    public Integer call() throws Exception {
        CheckpointMetadata metadata = FlinkCheckpointInspector.loadMetadata(checkpointPath);
        listOperators(metadata);
        return 0;
    }

    private void listOperators(CheckpointMetadata metadata) {
        System.out.println("=== Operators ===");
        System.out.println();

        int operatorIndex = 0;
        for (OperatorState operatorState : metadata.getOperatorStates()) {
            System.out.println("Operator " + operatorIndex + ":");
            System.out.println("  ID: " + operatorState.getOperatorID());
            System.out.println("  Hex ID: " + operatorState.getOperatorID().toHexString());
            System.out.println("  Parallelism: " + operatorState.getParallelism());
            System.out.println("  Max Parallelism: " + operatorState.getMaxParallelism());
            System.out.println("  Fully Finished: " + operatorState.isFullyFinished());

            if (operatorState.getCoordinatorState() != null) {
                System.out.println(
                        "  Coordinator State: "
                                + CheckpointMetadataUtils.formatBytes(
                                        operatorState.getCoordinatorState().getStateSize()));
            }

            System.out.println("  Subtasks (" + operatorState.getSubtaskStates().size() + "):");

            for (Map.Entry<Integer, OperatorSubtaskState> entry : operatorState.getSubtaskStates().entrySet()) {
                FlinkCheckpointInspector.printSubtaskStateInfo(entry.getKey(), entry.getValue());
            }

            System.out.println();
            operatorIndex++;
        }
    }
}
