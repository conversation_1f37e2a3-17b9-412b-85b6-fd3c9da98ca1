/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.tools.checkpoint.commands;

import org.apache.flink.core.fs.FSDataInputStream;
import org.apache.flink.core.memory.DataInputViewStreamWrapper;
import org.apache.flink.runtime.checkpoint.OperatorState;
import org.apache.flink.runtime.checkpoint.OperatorSubtaskState;
import org.apache.flink.runtime.checkpoint.metadata.CheckpointMetadata;
import org.apache.flink.runtime.state.OperatorStateHandle;
import org.apache.flink.runtime.state.OperatorStreamStateHandle;
import org.apache.flink.tools.checkpoint.FlinkCheckpointInspector;
import org.apache.flink.tools.checkpoint.utils.CheckpointMetadataUtils;
import picocli.CommandLine;

import java.util.Map;

/**
 * Command to inspect operator state for a specific operator.
 */
@CommandLine.Command(name = "operator-state", description = "Inspect operator state for specific operator")
public class OperatorStateCommand implements Command {

    @CommandLine.Parameters(index = "0", description = "The path to the checkpoint.")
    private String checkpointPath;

    @CommandLine.Option(names = {"-o", "--operator-id"}, required = true, description = "Operator ID")
    private String operatorId;

    @CommandLine.Option(names = {"-s", "--subtask"}, description = "Subtask index (default: 0)", defaultValue = "0")
    private int subtaskIndex;

    @CommandLine.Option(names = {"-v", "--verbose"}, description = "Verbose output")
    private boolean verbose;

    @Override
    public Integer call() throws Exception {
        CheckpointMetadata metadata = FlinkCheckpointInspector.loadMetadata(checkpointPath);
        OperatorState operatorState = FlinkCheckpointInspector.findOperatorById(metadata, operatorId);

        if (operatorState == null) {
            System.err.println("Operator not found: " + operatorId);
            return 1;
        }

        inspectOperatorState(operatorState, subtaskIndex, verbose);
        return 0;
    }

    private void inspectOperatorState(OperatorState operatorState, int subtaskIndex, boolean verbose) throws Exception {
        System.out.println("=== Operator State Inspection ===");
        System.out.println("Operator ID: " + operatorState.getOperatorID());
        System.out.println("Subtask: " + subtaskIndex);
        System.out.println();

        OperatorSubtaskState subtaskState = operatorState.getSubtaskStates().get(subtaskIndex);
        if (subtaskState == null) {
            System.err.println("Subtask not found: " + subtaskIndex);
            return;
        }

        // Inspect managed operator state
        if (!subtaskState.getManagedOperatorState().isEmpty()) {
            System.out.println("Managed Operator State:");
            for (OperatorStateHandle handle : subtaskState.getManagedOperatorState()) {
                inspectOperatorStateHandle(handle, verbose);
            }
        }

        // Inspect raw operator state
        if (!subtaskState.getRawOperatorState().isEmpty()) {
            System.out.println("Raw Operator State:");
            for (OperatorStateHandle handle : subtaskState.getRawOperatorState()) {
                inspectOperatorStateHandle(handle, verbose);
            }
        }

        if (subtaskState.getManagedOperatorState().isEmpty() && subtaskState.getRawOperatorState().isEmpty()) {
            System.out.println("No operator state found for this subtask.");
        }
    }

    private void inspectOperatorStateHandle(OperatorStateHandle handle, boolean verbose) throws Exception {
        System.out.println("  Handle Type: " + handle.getClass().getSimpleName());
        System.out.println("  State Size: " + CheckpointMetadataUtils.formatBytes(handle.getStateSize()));
        System.out.println("  State Handle ID: " + handle.getStreamStateHandleID());

        if (handle.getStateNameToPartitionOffsets() != null) {
            System.out.println("  State Names and Partitions:");
            for (Map.Entry<String, OperatorStateHandle.StateMetaInfo> entry : 
                 handle.getStateNameToPartitionOffsets().entrySet()) {
                String stateName = entry.getKey();
                OperatorStateHandle.StateMetaInfo metaInfo = entry.getValue();
                
                System.out.println("    " + stateName + ":");
                System.out.println("      Distribution Mode: " + metaInfo.getDistributionMode());
                System.out.println("      Partitions: " + metaInfo.getOffsets().length);
                
                if (verbose) {
                    System.out.println("      Partition Offsets:");
                    for (int i = 0; i < metaInfo.getOffsets().length; i++) {
                        System.out.println("        Partition " + i + ": offset " + metaInfo.getOffsets()[i]);
                    }
                }
            }
        }

        if (verbose && handle instanceof OperatorStreamStateHandle) {
            OperatorStreamStateHandle streamHandle = (OperatorStreamStateHandle) handle;
            try {
                inspectOperatorStreamStateHandle(streamHandle);
            } catch (Exception e) {
                System.out.println("  Error reading state data: " + e.getMessage());
            }
        }

        System.out.println();
    }

    private void inspectOperatorStreamStateHandle(OperatorStreamStateHandle handle) throws Exception {
        System.out.println("  Stream State Details:");
        
        try (FSDataInputStream inputStream = handle.openInputStream()) {
            DataInputViewStreamWrapper inView = new DataInputViewStreamWrapper(inputStream);
            
            // Try to read some basic information
            // Note: The exact format depends on how the state was serialized
            System.out.println("    Stream available: " + inputStream.available() + " bytes");
            
            // For demonstration, we'll just show the first few bytes
            byte[] header = new byte[Math.min(16, (int) handle.getStateSize())];
            inputStream.seek(0);
            inputStream.read(header);
            
            System.out.print("    Header bytes: ");
            for (byte b : header) {
                System.out.printf("%02x ", b);
            }
            System.out.println();
        }
    }
}
