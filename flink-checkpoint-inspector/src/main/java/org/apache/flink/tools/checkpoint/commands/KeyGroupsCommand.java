/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.tools.checkpoint.commands;

import org.apache.flink.runtime.checkpoint.OperatorState;
import org.apache.flink.runtime.checkpoint.OperatorSubtaskState;
import org.apache.flink.runtime.checkpoint.metadata.CheckpointMetadata;
import org.apache.flink.runtime.state.KeyGroupRange;
import org.apache.flink.runtime.state.KeyGroupsStateHandle;
import org.apache.flink.runtime.state.KeyedStateHandle;
import org.apache.flink.tools.checkpoint.FlinkCheckpointInspector;

import org.apache.flink.tools.checkpoint.utils.CheckpointMetadataUtils;
import picocli.CommandLine;

import java.util.*;

/**
 * Command to show key group distribution across operators and subtasks.
 */
@CommandLine.Command(name = "key-groups", description = "Show key group distribution across operators and subtasks")
public class KeyGroupsCommand implements Command {

    @CommandLine.Parameters(index = "0", description = "The path to the checkpoint.")
    private String checkpointPath;

    @Override
    public Integer call() throws Exception {
        CheckpointMetadata metadata = FlinkCheckpointInspector.loadMetadata(checkpointPath);
        analyzeKeyGroups(metadata);
        return 0;
    }

    private void analyzeKeyGroups(CheckpointMetadata metadata) {
        System.out.println("=== Key Group Distribution Analysis ===");
        System.out.println();

        Map<Integer, List<KeyGroupInfo>> keyGroupToOperators = new HashMap<>();
        int totalKeyGroups = 0;
        int maxKeyGroup = -1;
        int minKeyGroup = Integer.MAX_VALUE;

        // Collect all key group information
        for (OperatorState operatorState : metadata.getOperatorStates()) {
            if (operatorState.getSubtaskStates().isEmpty()) {
                continue;
            }

            System.out.println("Operator: " + operatorState.getOperatorID());
            System.out.println("  Max Parallelism: " + operatorState.getMaxParallelism());
            System.out.println("  Current Parallelism: " + operatorState.getParallelism());
            System.out.println();

            for (Map.Entry<Integer, OperatorSubtaskState> entry : operatorState.getSubtaskStates().entrySet()) {
                int subtaskIndex = entry.getKey();
                OperatorSubtaskState subtaskState = entry.getValue();

                System.out.println("  Subtask " + subtaskIndex + ":");

                // Analyze managed keyed state
                for (KeyedStateHandle handle : subtaskState.getManagedKeyedState()) {
                    if (handle instanceof KeyGroupsStateHandle) {
                        KeyGroupsStateHandle keyGroupsHandle = (KeyGroupsStateHandle) handle;
                        KeyGroupRange range = keyGroupsHandle.getKeyGroupRange();

                        System.out.println("    Key Groups: " + range.getStartKeyGroup() + 
                                         " to " + range.getEndKeyGroup() + 
                                         " (count: " + range.getNumberOfKeyGroups() + ")");

                        totalKeyGroups += range.getNumberOfKeyGroups();
                        maxKeyGroup = Math.max(maxKeyGroup, range.getEndKeyGroup());
                        minKeyGroup = Math.min(minKeyGroup, range.getStartKeyGroup());

                        // Track which operators use which key groups
                        for (int keyGroup : range) {
                            keyGroupToOperators.computeIfAbsent(keyGroup, k -> new ArrayList<>())
                                .add(new KeyGroupInfo(operatorState.getOperatorID().toString(), 
                                                    subtaskIndex, handle.getStateSize()));
                        }
                    }
                }

                // Analyze raw keyed state
                for (KeyedStateHandle handle : subtaskState.getRawKeyedState()) {
                    if (handle instanceof KeyGroupsStateHandle) {
                        KeyGroupsStateHandle keyGroupsHandle = (KeyGroupsStateHandle) handle;
                        KeyGroupRange range = keyGroupsHandle.getKeyGroupRange();

                        System.out.println("    Raw Key Groups: " + range.getStartKeyGroup() + 
                                         " to " + range.getEndKeyGroup() + 
                                         " (count: " + range.getNumberOfKeyGroups() + ")");
                    }
                }
            }
            System.out.println();
        }

        // Summary statistics
        System.out.println("=== Key Group Summary ===");
        System.out.println("Total Key Groups Found: " + totalKeyGroups);
        if (maxKeyGroup >= 0) {
            System.out.println("Key Group Range: " + minKeyGroup + " to " + maxKeyGroup);
            System.out.println("Total Key Group Space: " + (maxKeyGroup - minKeyGroup + 1));
        }
        System.out.println();

        // Key group distribution analysis
        if (!keyGroupToOperators.isEmpty()) {
            System.out.println("=== Key Group Distribution Details ===");
            
            // Show key groups with multiple operators (potential issues)
            List<Integer> sharedKeyGroups = new ArrayList<>();
            for (Map.Entry<Integer, List<KeyGroupInfo>> entry : keyGroupToOperators.entrySet()) {
                if (entry.getValue().size() > 1) {
                    sharedKeyGroups.add(entry.getKey());
                }
            }

            if (!sharedKeyGroups.isEmpty()) {
                System.out.println("WARNING: Key groups shared by multiple operators:");
                for (Integer keyGroup : sharedKeyGroups) {
                    System.out.println("  Key Group " + keyGroup + ":");
                    for (KeyGroupInfo info : keyGroupToOperators.get(keyGroup)) {
                        System.out.println("    - Operator: " + info.operatorId + 
                                         ", Subtask: " + info.subtaskIndex + 
                                         ", Size: " + CheckpointMetadataUtils.formatBytes(info.stateSize));
                    }
                }
                System.out.println();
            }

            // Show key group coverage
            Set<Integer> allKeyGroups = keyGroupToOperators.keySet();
            if (!allKeyGroups.isEmpty()) {
                int minFound = Collections.min(allKeyGroups);
                int maxFound = Collections.max(allKeyGroups);
                
                List<Integer> missingKeyGroups = new ArrayList<>();
                for (int i = minFound; i <= maxFound; i++) {
                    if (!allKeyGroups.contains(i)) {
                        missingKeyGroups.add(i);
                    }
                }

                if (!missingKeyGroups.isEmpty()) {
                    System.out.println("Missing Key Groups in Range [" + minFound + ", " + maxFound + "]:");
                    System.out.println("  " + missingKeyGroups);
                    System.out.println();
                }
            }

            // Show distribution by operator
            Map<String, Set<Integer>> operatorKeyGroups = new HashMap<>();
            for (Map.Entry<Integer, List<KeyGroupInfo>> entry : keyGroupToOperators.entrySet()) {
                for (KeyGroupInfo info : entry.getValue()) {
                    operatorKeyGroups.computeIfAbsent(info.operatorId, k -> new HashSet<>())
                        .add(entry.getKey());
                }
            }

            System.out.println("Key Groups by Operator:");
            for (Map.Entry<String, Set<Integer>> entry : operatorKeyGroups.entrySet()) {
                System.out.println("  " + entry.getKey() + ": " + entry.getValue().size() + " key groups");
                
                // Show ranges for better readability
                List<Integer> sortedKeyGroups = new ArrayList<>(entry.getValue());
                Collections.sort(sortedKeyGroups);
                System.out.println("    Ranges: " + formatRanges(sortedKeyGroups));
            }
        }
    }

    private String formatRanges(List<Integer> sortedNumbers) {
        if (sortedNumbers.isEmpty()) {
            return "[]";
        }

        StringBuilder sb = new StringBuilder();
        int start = sortedNumbers.get(0);
        int end = start;

        for (int i = 1; i < sortedNumbers.size(); i++) {
            int current = sortedNumbers.get(i);
            if (current == end + 1) {
                end = current;
            } else {
                if (sb.length() > 0) {
                    sb.append(", ");
                }
                if (start == end) {
                    sb.append(start);
                } else {
                    sb.append(start).append("-").append(end);
                }
                start = end = current;
            }
        }

        if (sb.length() > 0) {
            sb.append(", ");
        }
        if (start == end) {
            sb.append(start);
        } else {
            sb.append(start).append("-").append(end);
        }

        return "[" + sb.toString() + "]";
    }

    

    private static class KeyGroupInfo {
        final String operatorId;
        final int subtaskIndex;
        final long stateSize;

        KeyGroupInfo(String operatorId, int subtaskIndex, long stateSize) {
            this.operatorId = operatorId;
            this.subtaskIndex = subtaskIndex;
            this.stateSize = stateSize;
        }
    }
}
