/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.tools.checkpoint.utils;

import org.apache.flink.core.fs.FileSystem;
import org.apache.flink.core.fs.Path;
import org.apache.flink.core.memory.DataInputViewStreamWrapper;
import org.apache.flink.runtime.checkpoint.Checkpoints;
import org.apache.flink.runtime.checkpoint.OperatorState;
import org.apache.flink.runtime.checkpoint.metadata.CheckpointMetadata;
import org.apache.flink.runtime.state.KeyedStateHandle;
import org.apache.flink.runtime.state.OperatorStateHandle;
import org.apache.flink.runtime.state.filesystem.AbstractFsCheckpointStorageAccess;
import org.apache.flink.util.CollectionUtil;

import java.io.DataInputStream;
import java.io.IOException;
import java.util.List;
import java.util.stream.Collectors;

/** A collection of utility methods for checkpoint metadata. */
public class CheckpointMetadataUtils {

    private CheckpointMetadataUtils() {}

    public static CheckpointMetadata loadMetadata(String checkpointPath) throws IOException {
        Path path = new Path(checkpointPath);
        FileSystem fileSystem = path.getFileSystem();

        // If the path is a directory, append the metadata file name
        if (fileSystem.getFileStatus(path).isDir()) {
            path = new Path(path, AbstractFsCheckpointStorageAccess.METADATA_FILE_NAME);
        }

        try (DataInputStream dis = new DataInputStream(fileSystem.open(path))) {
            return Checkpoints.loadCheckpointMetadata(dis, Thread.currentThread().getContextClassLoader(), checkpointPath);
        }
    }

    public static boolean hasKeyedState(OperatorState operatorState) {
        return !CollectionUtil.isNullOrEmpty(operatorState.getStates());
    }

    public static boolean hasOperatorState(OperatorState operatorState) {
        return operatorState.getStates().stream()
                .anyMatch(subtaskState ->
                    !subtaskState.getManagedOperatorState().isEmpty() ||
                    !subtaskState.getRawOperatorState().isEmpty());
    }

    public static List<OperatorState> getOperatorsWithKeyedState(CheckpointMetadata metadata) {
        return metadata.getOperatorStates().stream()
                .filter(CheckpointMetadataUtils::hasKeyedState)
                .collect(Collectors.toList());
    }

    public static List<OperatorState> getOperatorsWithOperatorState(CheckpointMetadata metadata) {
        return metadata.getOperatorStates().stream()
                .filter(CheckpointMetadataUtils::hasOperatorState)
                .collect(Collectors.toList());
    }

    public static String formatBytes(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        }
        int exp = (int) (Math.log(bytes) / Math.log(1024));
        char pre = "KMGTPE".charAt(exp - 1);
        return String.format("%.1f %cB", bytes / Math.pow(1024, exp), pre);
    }

    /** A simple data class to hold summary statistics for a checkpoint. */
    public static class CheckpointSummary {
        private final long totalSize;
        private final int keyedStateCount;
        private final int operatorStateCount;

        public CheckpointSummary(long totalSize, int keyedStateCount, int operatorStateCount) {
            this.totalSize = totalSize;
            this.keyedStateCount = keyedStateCount;
            this.operatorStateCount = operatorStateCount;
        }

        public long getTotalSize() {
            return totalSize;
        }

        public int getKeyedStateCount() {
            return keyedStateCount;
        }

        public int getOperatorStateCount() {
            return operatorStateCount;
        }
    }
}