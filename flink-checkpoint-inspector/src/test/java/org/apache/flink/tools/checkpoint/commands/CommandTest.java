/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.tools.checkpoint.commands;

import org.junit.jupiter.api.Test;

import java.util.concurrent.Callable;

import static org.assertj.core.api.Assertions.assertThat;

/**
 * Tests for the Command interface.
 */
class CommandTest {

    @Test
    void testCommandExtendsCallable() {
        // Verify that Command interface extends Callable<Integer>
        assertThat(Callable.class).isAssignableFrom(Command.class);
    }

    @Test
    void testCommandImplementation() throws Exception {
        // Create a simple test implementation
        Command testCommand = new Command() {
            @Override
            public Integer call() throws Exception {
                return 42;
            }
        };

        // Test that it can be called as a Callable
        Callable<Integer> callable = testCommand;
        Integer result = callable.call();
        assertThat(result).isEqualTo(42);
    }

    @Test
    void testCommandCanThrowException() {
        Command testCommand = new Command() {
            @Override
            public Integer call() throws Exception {
                throw new RuntimeException("Test exception");
            }
        };

        // Verify that exceptions can be thrown
        org.junit.jupiter.api.Assertions.assertThrows(RuntimeException.class, testCommand::call);
    }

    @Test
    void testCommandReturnTypes() throws Exception {
        // Test successful execution (return 0)
        Command successCommand = () -> 0;
        assertThat(successCommand.call()).isEqualTo(0);

        // Test error execution (return non-zero)
        Command errorCommand = () -> 1;
        assertThat(errorCommand.call()).isEqualTo(1);

        // Test custom return code
        Command customCommand = () -> 123;
        assertThat(customCommand.call()).isEqualTo(123);
    }
}
