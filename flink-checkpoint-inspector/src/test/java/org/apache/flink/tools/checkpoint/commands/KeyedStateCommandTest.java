/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.tools.checkpoint.commands;

import org.apache.flink.runtime.checkpoint.OperatorState;
import org.apache.flink.tools.checkpoint.FlinkCheckpointInspector;

import org.junit.jupiter.api.Test;
import picocli.CommandLine;

import java.io.IOException;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * Tests for KeyedStateCommand.
 */
class KeyedStateCommandTest extends BaseCommandTest {

    @Test
    void testSuccessfulExecution() throws Exception {
        KeyedStateCommand command = new KeyedStateCommand();
        
        // Set required fields
        setField(command, "checkpointPath", testCheckpointPath);
        setField(command, "operatorId", getFirstOperatorState(mockMetadata).getOperatorID().toString());
        setField(command, "subtaskIndex", 0);
        setField(command, "verbose", false);

        String output = captureOutput(() -> {
            try {
                Integer result = command.call();
                assertThat(result).isEqualTo(0);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });

        // Verify output contains expected keyed state information
        assertOutputContains(output, "=== Keyed State Inspection ===");
        assertOutputContains(output, "Operator ID:");
        assertOutputContains(output, "Subtask: 0");
        assertOutputContains(output, "Managed Keyed State:");
    }

    @Test
    void testWithVerboseMode() throws Exception {
        KeyedStateCommand command = new KeyedStateCommand();
        
        setField(command, "checkpointPath", testCheckpointPath);
        setField(command, "operatorId", getFirstOperatorState(mockMetadata).getOperatorID().toString());
        setField(command, "subtaskIndex", 0);
        setField(command, "verbose", true);

        String output = captureOutput(() -> {
            try {
                Integer result = command.call();
                assertThat(result).isEqualTo(0);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });

        assertOutputContains(output, "=== Keyed State Inspection ===");
        assertOutputContains(output, "Verbose: true");
    }

    @Test
    void testWithPicocliCommandLine() throws Exception {
        CommandLine commandLine = new CommandLine(new KeyedStateCommand());
        String operatorId = getFirstOperatorState(mockMetadata).getOperatorID().toString();
        
        String output = captureOutput(() -> {
            int exitCode = commandLine.execute(
                testCheckpointPath, 
                "--operator-id", operatorId,
                "--subtask", "0"
            );
            assertThat(exitCode).isEqualTo(0);
        });

        assertOutputContains(output, "=== Keyed State Inspection ===");
    }

    @Test
    void testOperatorNotFound() throws Exception {
        // Mock findOperatorById to return null
        mockedInspector.when(() -> FlinkCheckpointInspector.findOperatorById(mockMetadata, "nonexistent"))
                .thenReturn(null);

        KeyedStateCommand command = new KeyedStateCommand();
        setField(command, "checkpointPath", testCheckpointPath);
        setField(command, "operatorId", "nonexistent");
        setField(command, "subtaskIndex", 0);

        Integer result = command.call();
        assertThat(result).isEqualTo(1);
        assertErrorContains("Operator not found: nonexistent");
    }

    @Test
    void testSubtaskNotFound() throws Exception {
        KeyedStateCommand command = new KeyedStateCommand();
        setField(command, "checkpointPath", testCheckpointPath);
        setField(command, "operatorId", getFirstOperatorState(mockMetadata).getOperatorID().toString());
        setField(command, "subtaskIndex", 999); // Non-existent subtask

        String output = captureOutput(() -> {
            try {
                Integer result = command.call();
                assertThat(result).isEqualTo(0);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });

        assertOutputContains(output, "Subtask not found: 999");
    }

    @Test
    void testErrorHandling() throws Exception {
        // Mock an IOException when loading metadata
        mockedInspector.when(() -> FlinkCheckpointInspector.loadMetadata(anyString()))
                .thenThrow(new IOException("Failed to load checkpoint"));

        KeyedStateCommand command = new KeyedStateCommand();
        setField(command, "checkpointPath", testCheckpointPath);
        setField(command, "operatorId", "test-operator");
        setField(command, "subtaskIndex", 0);

        Exception exception = org.junit.jupiter.api.Assertions.assertThrows(
                Exception.class, command::call);
        assertThat(exception).isInstanceOf(IOException.class);
        assertThat(exception.getMessage()).contains("Failed to load checkpoint");
    }

    @Test
    void testNoKeyedState() throws Exception {
        // Create an operator state with no keyed state
        OperatorState operatorWithoutKeyedState = new org.apache.flink.runtime.checkpoint.OperatorState(
                new org.apache.flink.runtime.jobgraph.OperatorID(),
                1, 128);
        operatorWithoutKeyedState.putState(0,
            org.apache.flink.runtime.checkpoint.OperatorSubtaskState.builder().build());

        mockedInspector.when(() -> FlinkCheckpointInspector.findOperatorById(mockMetadata, "empty-operator"))
                .thenReturn(operatorWithoutKeyedState);

        KeyedStateCommand command = new KeyedStateCommand();
        setField(command, "checkpointPath", testCheckpointPath);
        setField(command, "operatorId", "empty-operator");
        setField(command, "subtaskIndex", 0);

        String output = captureOutput(() -> {
            try {
                Integer result = command.call();
                assertThat(result).isEqualTo(0);
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });

        assertOutputContains(output, "No keyed state found for this subtask");
    }

    @Test
    void testCommandAnnotations() {
        // Verify the command is properly annotated
        CommandLine.Command annotation = KeyedStateCommand.class.getAnnotation(CommandLine.Command.class);
        assertThat(annotation).isNotNull();
        assertThat(annotation.name()).isEqualTo("keyed-state");
        assertThat(annotation.description()).isEqualTo("Inspect keyed state for specific operator and subtask");
    }

    @Test
    void testOptionAnnotations() throws Exception {
        // Verify the operator ID option
        java.lang.reflect.Field operatorIdField = KeyedStateCommand.class.getDeclaredField("operatorId");
        CommandLine.Option operatorIdAnnotation = operatorIdField.getAnnotation(CommandLine.Option.class);
        assertThat(operatorIdAnnotation).isNotNull();
        assertThat(operatorIdAnnotation.names()).containsExactly("-o", "--operator-id");
        assertThat(operatorIdAnnotation.required()).isTrue();

        // Verify the subtask option
        java.lang.reflect.Field subtaskField = KeyedStateCommand.class.getDeclaredField("subtaskIndex");
        CommandLine.Option subtaskAnnotation = subtaskField.getAnnotation(CommandLine.Option.class);
        assertThat(subtaskAnnotation).isNotNull();
        assertThat(subtaskAnnotation.names()).containsExactly("-s", "--subtask");
        assertThat(subtaskAnnotation.defaultValue()).isEqualTo("0");

        // Verify the verbose option
        java.lang.reflect.Field verboseField = KeyedStateCommand.class.getDeclaredField("verbose");
        CommandLine.Option verboseAnnotation = verboseField.getAnnotation(CommandLine.Option.class);
        assertThat(verboseAnnotation).isNotNull();
        assertThat(verboseAnnotation.names()).containsExactly("-v", "--verbose");
    }

    /**
     * Helper method to set private fields using reflection.
     */
    private void setField(Object target, String fieldName, Object value) throws Exception {
        java.lang.reflect.Field field = target.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(target, value);
    }
}
