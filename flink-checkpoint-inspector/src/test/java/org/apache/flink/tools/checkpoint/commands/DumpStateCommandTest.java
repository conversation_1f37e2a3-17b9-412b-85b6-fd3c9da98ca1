/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.tools.checkpoint.commands;

import org.apache.flink.runtime.checkpoint.OperatorState;
import org.apache.flink.runtime.state.KeyGroupsStateHandle;
import org.apache.flink.tools.checkpoint.FlinkCheckpointInspector;
import org.apache.flink.tools.checkpoint.utils.StateDataReader;

import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import picocli.CommandLine;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.HashMap;
import java.util.Map;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.mockStatic;

import static org.mockito.Mockito.when;

/**
 * Tests for DumpStateCommand.
 */
class DumpStateCommandTest extends BaseCommandTest {

    @Test
    void testSuccessfulExecutionJsonFormat() throws Exception {
        DumpStateCommand command = new DumpStateCommand();
        
        // Set required fields
        setField(command, "checkpointPath", testCheckpointPath);
        setField(command, "operatorId", getFirstOperatorState(mockMetadata).getOperatorID().toString());
        setField(command, "subtaskIndex", 0);
        setField(command, "format", "json");
        setField(command, "limit", -1);

        try (MockedStatic<FlinkCheckpointInspector> mockedInspector = createMockedInspector()) {
            String output = captureOutput(() -> {
                try {
                    Integer result = command.call();
                    assertThat(result).isEqualTo(0);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });

            // Verify output contains expected dump information
            assertOutputContains(output, "=== Dumping State Data ===");
            assertOutputContains(output, "Operator ID:");
            assertOutputContains(output, "Subtask: 0");
            assertOutputContains(output, "Format: json");
            assertOutputContains(output, "Output: stdout");
        }
    }

    @Test
    void testSuccessfulExecutionCsvFormat() throws Exception {
        DumpStateCommand command = new DumpStateCommand();
        
        setField(command, "checkpointPath", testCheckpointPath);
        setField(command, "operatorId", getFirstOperatorState(mockMetadata).getOperatorID().toString());
        setField(command, "subtaskIndex", 0);
        setField(command, "format", "csv");
        setField(command, "limit", -1);

        try (MockedStatic<FlinkCheckpointInspector> mockedInspector = createMockedInspector()) {
            String output = captureOutput(() -> {
                try {
                    Integer result = command.call();
                    assertThat(result).isEqualTo(0);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });

            assertOutputContains(output, "Format: csv");
        }
    }

    @Test
    void testFileOutput() throws Exception {
        Path outputFile = tempDir.resolve("test-output.json");
        
        DumpStateCommand command = new DumpStateCommand();
        setField(command, "checkpointPath", testCheckpointPath);
        setField(command, "operatorId", getFirstOperatorState(mockMetadata).getOperatorID().toString());
        setField(command, "subtaskIndex", 0);
        setField(command, "outputFile", outputFile.toString());
        setField(command, "format", "json");
        setField(command, "limit", -1);

        try (MockedStatic<FlinkCheckpointInspector> mockedInspector = createMockedInspector()) {
            String output = captureOutput(() -> {
                try {
                    Integer result = command.call();
                    assertThat(result).isEqualTo(0);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });

            assertOutputContains(output, "Output: " + outputFile.toString());
            assertOutputContains(output, "State data written to: " + outputFile.toString());
            
            // Verify file was created
            assertThat(Files.exists(outputFile)).isTrue();
        }
    }

    @Test
    void testWithLimit() throws Exception {
        DumpStateCommand command = new DumpStateCommand();
        
        setField(command, "checkpointPath", testCheckpointPath);
        setField(command, "operatorId", getFirstOperatorState(mockMetadata).getOperatorID().toString());
        setField(command, "subtaskIndex", 0);
        setField(command, "format", "json");
        setField(command, "limit", 2);

        try (MockedStatic<FlinkCheckpointInspector> mockedInspector = createMockedInspector()) {
            Integer result = command.call();
            assertThat(result).isEqualTo(0);
        }
    }

    @Test
    void testWithPicocliCommandLine() throws Exception {
        CommandLine commandLine = new CommandLine(new DumpStateCommand());
        String operatorId = getFirstOperatorState(mockMetadata).getOperatorID().toString();
        
        try (MockedStatic<FlinkCheckpointInspector> mockedInspector = createMockedInspector()) {
            String output = captureOutput(() -> {
                int exitCode = commandLine.execute(
                    testCheckpointPath,
                    "--operator-id", operatorId,
                    "--format", "json"
                );
                assertThat(exitCode).isEqualTo(0);
            });

            assertOutputContains(output, "=== Dumping State Data ===");
        }
    }

    @Test
    void testOperatorNotFound() throws Exception {
        try (MockedStatic<FlinkCheckpointInspector> mockedInspector = createMockedInspector()) {
            // Mock findOperatorById to return null
            mockedInspector.when(() -> FlinkCheckpointInspector.findOperatorById(mockMetadata, "nonexistent"))
                    .thenReturn(null);

            DumpStateCommand command = new DumpStateCommand();
            setField(command, "checkpointPath", testCheckpointPath);
            setField(command, "operatorId", "nonexistent");
            setField(command, "subtaskIndex", 0);

            Integer result = command.call();
            assertThat(result).isEqualTo(1);
            assertErrorContains("Operator not found: nonexistent");
        }
    }

    @Test
    void testUnsupportedFormat() throws Exception {
        try (MockedStatic<FlinkCheckpointInspector> mockedInspector = createMockedInspector()) {
            DumpStateCommand command = new DumpStateCommand();
            setField(command, "checkpointPath", testCheckpointPath);
            setField(command, "operatorId", getFirstOperatorState(mockMetadata).getOperatorID().toString());
            setField(command, "subtaskIndex", 0);
            setField(command, "format", "xml"); // Unsupported format

            Integer result = command.call();
            assertThat(result).isEqualTo(1);
            assertErrorContains("Unsupported format: xml");
        }
    }

    @Test
    void testSubtaskNotFound() throws Exception {
        try (MockedStatic<FlinkCheckpointInspector> mockedInspector = createMockedInspector()) {
            DumpStateCommand command = new DumpStateCommand();
            setField(command, "checkpointPath", testCheckpointPath);
            setField(command, "operatorId", getFirstOperatorState(mockMetadata).getOperatorID().toString());
            setField(command, "subtaskIndex", 999); // Non-existent subtask
            setField(command, "format", "json");

            String output = captureOutput(() -> {
                try {
                    Integer result = command.call();
                    assertThat(result).isEqualTo(0);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });

            assertOutputContains(output, "Subtask not found: 999");
        }
    }

    @Test
    void testErrorHandling() throws Exception {
        try (MockedStatic<FlinkCheckpointInspector> mockedInspector = mockStatic(FlinkCheckpointInspector.class)) {
            // Mock an IOException when loading metadata
            mockedInspector.when(() -> FlinkCheckpointInspector.loadMetadata(anyString()))
                    .thenThrow(new IOException("Failed to load checkpoint"));

            DumpStateCommand command = new DumpStateCommand();
            setField(command, "checkpointPath", testCheckpointPath);
            setField(command, "operatorId", "test-operator");
            setField(command, "subtaskIndex", 0);

            Exception exception = org.junit.jupiter.api.Assertions.assertThrows(
                    Exception.class, command::call);
            assertThat(exception).isInstanceOf(IOException.class);
            assertThat(exception.getMessage()).contains("Failed to load checkpoint");
        }
    }

    @Test
    void testCommandAnnotations() {
        // Verify the command is properly annotated
        CommandLine.Command annotation = DumpStateCommand.class.getAnnotation(CommandLine.Command.class);
        assertThat(annotation).isNotNull();
        assertThat(annotation.name()).isEqualTo("dump-state");
        assertThat(annotation.description()).containsExactly("Dump state data to a file or stdout.");
    }

    @Test
    void testOptionAnnotations() throws Exception {
        // Verify the operator ID option
        java.lang.reflect.Field operatorIdField = DumpStateCommand.class.getDeclaredField("operatorId");
        CommandLine.Option operatorIdAnnotation = operatorIdField.getAnnotation(CommandLine.Option.class);
        assertThat(operatorIdAnnotation).isNotNull();
        assertThat(operatorIdAnnotation.names()).containsExactly("-o", "--operator-id");
        assertThat(operatorIdAnnotation.required()).isTrue();

        // Verify the format option
        java.lang.reflect.Field formatField = DumpStateCommand.class.getDeclaredField("format");
        CommandLine.Option formatAnnotation = formatField.getAnnotation(CommandLine.Option.class);
        assertThat(formatAnnotation).isNotNull();
        assertThat(formatAnnotation.names()).containsExactly("--format");
        assertThat(formatAnnotation.defaultValue()).isEqualTo("json");

        // Verify the limit option
        java.lang.reflect.Field limitField = DumpStateCommand.class.getDeclaredField("limit");
        CommandLine.Option limitAnnotation = limitField.getAnnotation(CommandLine.Option.class);
        assertThat(limitAnnotation).isNotNull();
        assertThat(limitAnnotation.names()).containsExactly("--limit");
        assertThat(limitAnnotation.defaultValue()).isEqualTo("-1");
    }

    /**
     * Creates mock state data for testing.
     */
    private Map<String, Object> createMockStateData() {
        Map<String, Object> data = new HashMap<>();
        data.put("key1", "value1");
        data.put("key2", "value2");
        data.put("key3", 123);
        return data;
    }

    /**
     * Helper method to set private fields using reflection.
     */
    private void setField(Object target, String fieldName, Object value) throws Exception {
        java.lang.reflect.Field field = target.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(target, value);
    }
}
