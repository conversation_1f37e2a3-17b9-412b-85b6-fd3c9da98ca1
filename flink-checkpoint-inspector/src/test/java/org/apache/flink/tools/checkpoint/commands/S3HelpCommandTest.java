/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.tools.checkpoint.commands;

import org.apache.flink.tools.checkpoint.utils.S3Configuration;

import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import picocli.CommandLine;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mockStatic;

/**
 * Tests for S3HelpCommand.
 */
class S3HelpCommandTest extends BaseCommandTest {

    @Test
    void testSuccessfulExecution() throws Exception {
        S3HelpCommand command = new S3HelpCommand();

        // Mock S3Configuration.printS3Help() to capture its output
        try (MockedStatic<S3Configuration> mockedS3Config = mockStatic(S3Configuration.class)) {
            mockedS3Config.when(() -> S3Configuration.printS3Help())
                    .thenAnswer(invocation -> {
                        System.out.println("=== S3 Configuration Help ===");
                        System.out.println("This tool supports reading checkpoints from S3.");
                        System.out.println("Configuration options:");
                        System.out.println("  AWS_ACCESS_KEY_ID - Your AWS access key");
                        System.out.println("  AWS_SECRET_ACCESS_KEY - Your AWS secret key");
                        System.out.println("  AWS_REGION - AWS region (default: us-east-1)");
                        System.out.println();
                        System.out.println("Example usage:");
                        System.out.println("  export AWS_ACCESS_KEY_ID=your-key");
                        System.out.println("  export AWS_SECRET_ACCESS_KEY=your-secret");
                        System.out.println("  flink-checkpoint-inspector metadata s3://bucket/checkpoint");
                        return null;
                    });

            String output = captureOutput(() -> {
                try {
                    Integer result = command.call();
                    assertThat(result).isEqualTo(0);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });

            // Verify output contains expected S3 help information
            assertOutputContains(output, "=== S3 Configuration Help ===");
            assertOutputContains(output, "AWS_ACCESS_KEY_ID");
            assertOutputContains(output, "AWS_SECRET_ACCESS_KEY");
            assertOutputContains(output, "AWS_REGION");
            assertOutputContains(output, "Example usage:");
            assertOutputContains(output, "s3://bucket/checkpoint");
        }
    }

    @Test
    void testWithPicocliCommandLine() throws Exception {
        CommandLine commandLine = new CommandLine(new S3HelpCommand());
        
        try (MockedStatic<S3Configuration> mockedS3Config = mockStatic(S3Configuration.class)) {
            mockedS3Config.when(() -> S3Configuration.printS3Help())
                    .thenAnswer(invocation -> {
                        System.out.println("S3 Help Output");
                        return null;
                    });

            String output = captureOutput(() -> {
                int exitCode = commandLine.execute();
                assertThat(exitCode).isEqualTo(0);
            });

            assertOutputContains(output, "S3 Help Output");
        }
    }

    @Test
    void testS3ConfigurationMethodCalled() throws Exception {
        S3HelpCommand command = new S3HelpCommand();

        try (MockedStatic<S3Configuration> mockedS3Config = mockStatic(S3Configuration.class)) {
            mockedS3Config.when(S3Configuration::printS3Help)
                    .thenAnswer(invocation -> {
                        System.out.println("Mock S3 help called");
                        return null;
                    });

            Integer result = command.call();
            
            assertThat(result).isEqualTo(0);
            // Verify that printS3Help was called
            mockedS3Config.verify(S3Configuration::printS3Help);
        }
    }

    @Test
    void testCommandAnnotations() {
        // Verify the command is properly annotated
        CommandLine.Command annotation = S3HelpCommand.class.getAnnotation(CommandLine.Command.class);
        assertThat(annotation).isNotNull();
        assertThat(annotation.name()).isEqualTo("s3-help");
        assertThat(annotation.description()).isEqualTo("Show S3 configuration help");
    }

    @Test
    void testCommandImplementsInterface() {
        // Verify that S3HelpCommand implements Command interface
        assertThat(Command.class).isAssignableFrom(S3HelpCommand.class);
    }

    @Test
    void testCommandIsCallable() throws Exception {
        S3HelpCommand command = new S3HelpCommand();
        
        // Verify that it can be used as a Callable
        java.util.concurrent.Callable<Integer> callable = command;
        
        try (MockedStatic<S3Configuration> mockedS3Config = mockStatic(S3Configuration.class)) {
            mockedS3Config.when(S3Configuration::printS3Help)
                    .thenAnswer(invocation -> {
                        System.out.println("Callable test");
                        return null;
                    });

            Integer result = callable.call();
            assertThat(result).isEqualTo(0);
        }
    }

    @Test
    void testExceptionHandling() throws Exception {
        S3HelpCommand command = new S3HelpCommand();

        try (MockedStatic<S3Configuration> mockedS3Config = mockStatic(S3Configuration.class)) {
            // Mock S3Configuration.printS3Help() to throw an exception
            mockedS3Config.when(S3Configuration::printS3Help)
                    .thenThrow(new RuntimeException("S3 configuration error"));

            // The command should propagate the exception
            Exception exception = org.junit.jupiter.api.Assertions.assertThrows(
                    RuntimeException.class, command::call);
            assertThat(exception.getMessage()).contains("S3 configuration error");
        }
    }

    @Test
    void testMultipleExecutions() throws Exception {
        S3HelpCommand command = new S3HelpCommand();

        try (MockedStatic<S3Configuration> mockedS3Config = mockStatic(S3Configuration.class)) {
            mockedS3Config.when(S3Configuration::printS3Help)
                    .thenAnswer(invocation -> {
                        System.out.println("S3 help execution");
                        return null;
                    });

            // Execute multiple times to ensure it's stateless
            Integer result1 = command.call();
            Integer result2 = command.call();
            
            assertThat(result1).isEqualTo(0);
            assertThat(result2).isEqualTo(0);
            
            // Verify printS3Help was called twice
            mockedS3Config.verify(S3Configuration::printS3Help);
            mockedS3Config.verify(S3Configuration::printS3Help);
        }
    }

    @Test
    void testCommandLineIntegration() throws Exception {
        // Test that the command works properly when integrated with CommandLine
        CommandLine commandLine = new CommandLine(new S3HelpCommand());
        
        try (MockedStatic<S3Configuration> mockedS3Config = mockStatic(S3Configuration.class)) {
            mockedS3Config.when(S3Configuration::printS3Help)
                    .thenAnswer(invocation -> {
                        System.out.println("Integration test output");
                        return null;
                    });

            String output = captureOutput(() -> {
                int exitCode = commandLine.execute();
                assertThat(exitCode).isEqualTo(0);
            });

            assertOutputContains(output, "Integration test output");
        }
    }
}
