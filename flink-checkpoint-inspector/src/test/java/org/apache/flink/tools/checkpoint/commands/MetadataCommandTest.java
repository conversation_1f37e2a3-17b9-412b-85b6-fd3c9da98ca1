/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.tools.checkpoint.commands;

import org.apache.flink.runtime.checkpoint.metadata.CheckpointMetadata;
import org.apache.flink.tools.checkpoint.FlinkCheckpointInspector;
import org.apache.flink.tools.checkpoint.TestUtils;

import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import picocli.CommandLine;

import java.io.IOException;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * Tests for MetadataCommand.
 */
class MetadataCommandTest extends BaseCommandTest {

    @Test
    void testSuccessfulExecution() throws Exception {
        try (MockedStatic<FlinkCheckpointInspector> mockedInspector = createMockedInspector()) {
            MetadataCommand command = new MetadataCommand();

            // Use reflection to set the checkpoint path since it's a @Parameters field
            java.lang.reflect.Field pathField = MetadataCommand.class.getDeclaredField("checkpointPath");
            pathField.setAccessible(true);
            pathField.set(command, testCheckpointPath);

            String output = captureOutput(() -> {
                try {
                    Integer result = command.call();
                    assertThat(result).isEqualTo(0);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });

            // Verify output contains expected metadata information
            assertOutputContains(output, "=== Checkpoint Metadata ===");
            assertOutputContains(output, "Checkpoint ID: 123");
            assertOutputContains(output, "Master States:");
            assertOutputContains(output, "test-master-state");
            assertOutputContains(output, "Operator States:");
            assertOutputContains(output, "Total Operators: 2");
            assertOutputContains(output, "Operator Details:");
        }
    }

    @Test
    void testWithPicocliCommandLine() throws Exception {
        try (MockedStatic<FlinkCheckpointInspector> mockedInspector = createMockedInspector()) {
            CommandLine commandLine = new CommandLine(new MetadataCommand());

            String output = captureOutput(() -> {
                int exitCode = commandLine.execute(testCheckpointPath);
                assertThat(exitCode).isEqualTo(0);
            });

            assertOutputContains(output, "=== Checkpoint Metadata ===");
            assertOutputContains(output, "Checkpoint ID: 123");
        }
    }

    @Test
    void testErrorHandling() throws Exception {
        try (MockedStatic<FlinkCheckpointInspector> mockedInspector = mockStatic(FlinkCheckpointInspector.class)) {
            // Mock an IOException when loading metadata
            mockedInspector.when(() -> FlinkCheckpointInspector.loadMetadata(anyString()))
                    .thenThrow(new IOException("Failed to load checkpoint"));

            MetadataCommand command = new MetadataCommand();
            java.lang.reflect.Field pathField = MetadataCommand.class.getDeclaredField("checkpointPath");
            pathField.setAccessible(true);
            pathField.set(command, testCheckpointPath);

            Exception exception = org.junit.jupiter.api.Assertions.assertThrows(
                    Exception.class, command::call);
            assertThat(exception).isInstanceOf(IOException.class);
            assertThat(exception.getMessage()).contains("Failed to load checkpoint");
        }
    }

    @Test
    void testMetadataWithNoMasterStates() throws Exception {
        try (MockedStatic<FlinkCheckpointInspector> mockedInspector = mockStatic(FlinkCheckpointInspector.class)) {
            // Create metadata without master states
            CheckpointMetadata metadataWithoutMasterStates = new CheckpointMetadata(
                    456L,
                    mockMetadata.getOperatorStates(),
                    java.util.Collections.emptyList(),
                    mockMetadata.getCheckpointProperties());

            mockedInspector.when(() -> FlinkCheckpointInspector.loadMetadata(anyString()))
                    .thenReturn(metadataWithoutMasterStates);

            MetadataCommand command = new MetadataCommand();
            java.lang.reflect.Field pathField = MetadataCommand.class.getDeclaredField("checkpointPath");
            pathField.setAccessible(true);
            pathField.set(command, testCheckpointPath);

            String output = captureOutput(() -> {
                try {
                    Integer result = command.call();
                    assertThat(result).isEqualTo(0);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });

            assertOutputContains(output, "Checkpoint ID: 456");
            assertOutputDoesNotContain(output, "Master States:");
            assertOutputContains(output, "Operator States:");
        }
    }

    @Test
    void testMetadataWithCheckpointProperties() throws Exception {
        try (MockedStatic<FlinkCheckpointInspector> mockedInspector = createMockedInspector()) {
            MetadataCommand command = new MetadataCommand();
            java.lang.reflect.Field pathField = MetadataCommand.class.getDeclaredField("checkpointPath");
            pathField.setAccessible(true);
            pathField.set(command, testCheckpointPath);

            String output = captureOutput(() -> {
                try {
                    Integer result = command.call();
                    assertThat(result).isEqualTo(0);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });

            assertOutputContains(output, "Checkpoint Properties:");
            assertOutputContains(output, "Checkpoint Type:");
            assertOutputContains(output, "Is Savepoint:");
        }
    }

    @Test
    void testOperatorDetailsOutput() throws Exception {
        try (MockedStatic<FlinkCheckpointInspector> mockedInspector = createMockedInspector()) {
            MetadataCommand command = new MetadataCommand();
            java.lang.reflect.Field pathField = MetadataCommand.class.getDeclaredField("checkpointPath");
            pathField.setAccessible(true);
            pathField.set(command, testCheckpointPath);

            String output = captureOutput(() -> {
                try {
                    command.call();
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });

            // Check that operator details are displayed
            assertOutputContains(output, "Operator ID:");
            assertOutputContains(output, "Parallelism:");
            assertOutputContains(output, "Max Parallelism:");
            assertOutputContains(output, "Subtasks:");
            assertOutputContains(output, "State Size:");
            assertOutputContains(output, "Fully Finished:");
        }
    }

    @Test
    void testCommandAnnotations() {
        // Verify the command is properly annotated
        CommandLine.Command annotation = MetadataCommand.class.getAnnotation(CommandLine.Command.class);
        assertThat(annotation).isNotNull();
        assertThat(annotation.name()).isEqualTo("metadata");
        assertThat(annotation.description()).isEqualTo("Show checkpoint metadata");
    }
}
