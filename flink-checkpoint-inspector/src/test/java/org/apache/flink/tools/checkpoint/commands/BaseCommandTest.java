/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.tools.checkpoint.commands;

import org.apache.flink.runtime.checkpoint.OperatorState;
import org.apache.flink.runtime.checkpoint.metadata.CheckpointMetadata;
import org.apache.flink.tools.checkpoint.FlinkCheckpointInspector;
import org.apache.flink.tools.checkpoint.TestUtils;

import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.io.TempDir;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintStream;
import java.nio.file.Path;
import java.util.ArrayList;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;

/**
 * Base test class for command tests providing common setup and utilities.
 */
public abstract class BaseCommandTest {

    @TempDir
    protected Path tempDir;

    protected TestUtils.OutputCapture outputCapture;
    protected ByteArrayOutputStream errorOutput;
    protected PrintStream originalErr;
    protected MockedStatic<FlinkCheckpointInspector> mockedInspector;

    protected CheckpointMetadata mockMetadata;
    protected String testCheckpointPath;

    @BeforeEach
    void setUp() throws IOException {
        // Set up output capture
        outputCapture = new TestUtils.OutputCapture();
        originalErr = System.err;
        errorOutput = new ByteArrayOutputStream();
        System.setErr(new PrintStream(errorOutput));

        // Create mock checkpoint metadata
        mockMetadata = TestUtils.createMockCheckpointMetadata();
        testCheckpointPath = "test://checkpoint/path";

        // Mock FlinkCheckpointInspector static methods
        mockedInspector = mockStatic(FlinkCheckpointInspector.class);
        mockedInspector.when(() -> FlinkCheckpointInspector.loadMetadata(anyString()))
                .thenReturn(mockMetadata);
        
        // Mock findOperatorById to return the first operator by default
        if (!mockMetadata.getOperatorStates().isEmpty()) {
            mockedInspector.when(() -> FlinkCheckpointInspector.findOperatorById(
                    Mockito.eq(mockMetadata), anyString()))
                    .thenReturn(new ArrayList<>(mockMetadata.getOperatorStates()).get(0));
        }
    }

    @AfterEach
    void tearDown() {
        // Restore original streams
        if (outputCapture != null) {
            outputCapture.stop();
        }
        System.setErr(originalErr);

        // Close mocked static
        if (mockedInspector != null) {
            mockedInspector.close();
        }
    }

    /**
     * Captures stdout output during command execution.
     */
    protected String captureOutput(Runnable command) {
        outputCapture.start();
        try {
            command.run();
        } finally {
            return outputCapture.stop();
        }
    }

    /**
     * Gets the captured error output.
     */
    protected String getErrorOutput() {
        return errorOutput.toString();
    }

    /**
     * Normalizes output for cross-platform testing.
     */
    protected String normalizeOutput(String output) {
        return TestUtils.normalizeLineEndings(output);
    }

    /**
     * Creates a temporary file with specified content.
     */
    protected Path createTempFile(String content) throws IOException {
        return TestUtils.createTempFile(content);
    }

    /**
     * Asserts that output contains expected text (case-insensitive).
     */
    protected void assertOutputContains(String output, String expectedText) {
        org.assertj.core.api.Assertions.assertThat(output.toLowerCase())
                .contains(expectedText.toLowerCase());
    }

    /**
     * Asserts that output does not contain specified text (case-insensitive).
     */
    protected void assertOutputDoesNotContain(String output, String unexpectedText) {
        org.assertj.core.api.Assertions.assertThat(output.toLowerCase())
                .doesNotContain(unexpectedText.toLowerCase());
    }

    /**
     * Asserts that error output contains expected text.
     */
    protected void assertErrorContains(String expectedText) {
        org.assertj.core.api.Assertions.assertThat(getErrorOutput())
                .contains(expectedText);
    }

    /**
     * Helper method to get the first operator state from metadata.
     */
    protected OperatorState getFirstOperatorState(CheckpointMetadata metadata) {
        return new ArrayList<>(metadata.getOperatorStates()).get(0);
    }
}
