/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.tools.checkpoint;

import org.apache.flink.runtime.state.KeyGroupsStateHandle;

import java.util.HashMap;
import java.util.Map;

/**
 * Mock implementation of StateDataReader for testing purposes.
 */
public class MockStateDataReader {
    
    private final KeyGroupsStateHandle handle;
    private final Map<String, Object> mockData;

    public MockStateDataReader(KeyGroupsStateHandle handle) {
        this.handle = handle;
        this.mockData = createMockStateData();
    }

    /**
     * Creates mock state data for testing.
     */
    private Map<String, Object> createMockStateData() {
        Map<String, Object> data = new HashMap<>();
        data.put("key1", "value1");
        data.put("key2", "value2");
        data.put("key3", 123);
        data.put("key4", 456.789);
        data.put("key5", true);
        return data;
    }

    /**
     * Reads state data for the specified state name.
     * Returns all mock data if stateName is null.
     */
    public Map<String, Object> readStateData(String stateName) {
        if (stateName == null) {
            return mockData;
        }
        
        // Filter data by state name (for testing, we'll return all data)
        return mockData;
    }

    /**
     * Gets the handle this reader is associated with.
     */
    public KeyGroupsStateHandle getHandle() {
        return handle;
    }
}
