/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.tools.checkpoint.commands;

import org.apache.flink.runtime.checkpoint.OperatorState;
import org.apache.flink.runtime.checkpoint.metadata.CheckpointMetadata;
import org.apache.flink.tools.checkpoint.FlinkCheckpointInspector;
import org.apache.flink.tools.checkpoint.TestUtils;

import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import picocli.CommandLine;

import java.io.IOException;
import java.util.Collections;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

/**
 * Tests for OperatorsCommand.
 */
class OperatorsCommandTest extends BaseCommandTest {

    @Test
    void testSuccessfulExecution() throws Exception {
        OperatorsCommand command = new OperatorsCommand();
        
        // Set the checkpoint path
        java.lang.reflect.Field pathField = OperatorsCommand.class.getDeclaredField("checkpointPath");
        pathField.setAccessible(true);
        pathField.set(command, testCheckpointPath);

        // Mock printSubtaskStateInfo to avoid complex state handle mocking
        try (MockedStatic<FlinkCheckpointInspector> mockedInspector = createMockedInspector()) {
            mockedInspector.when(() -> FlinkCheckpointInspector.printSubtaskStateInfo(anyInt(), any()))
                    .thenAnswer(invocation -> {
                        int subtaskIndex = invocation.getArgument(0);
                        System.out.println("    Subtask " + subtaskIndex + ":");
                        System.out.println("      Mock state info");
                        return null;
                    });

            String output = captureOutput(() -> {
                try {
                    Integer result = command.call();
                    assertThat(result).isEqualTo(0);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });

            // Verify output contains expected operator information
            assertOutputContains(output, "=== Operators ===");
            assertOutputContains(output, "Operator 0:");
            assertOutputContains(output, "Operator 1:");
            assertOutputContains(output, "ID:");
            assertOutputContains(output, "Hex ID:");
            assertOutputContains(output, "Parallelism:");
            assertOutputContains(output, "Max Parallelism:");
            assertOutputContains(output, "Fully Finished:");
            assertOutputContains(output, "Subtasks");
        }
    }

    @Test
    void testWithPicocliCommandLine() throws Exception {
        CommandLine commandLine = new CommandLine(new OperatorsCommand());
        
        // Mock printSubtaskStateInfo
        try (MockedStatic<FlinkCheckpointInspector> mockedInspector = createMockedInspector()) {
            mockedInspector.when(() -> FlinkCheckpointInspector.printSubtaskStateInfo(anyInt(), any()))
                    .thenAnswer(invocation -> {
                        System.out.println("    Mock subtask info");
                        return null;
                    });

            String output = captureOutput(() -> {
                int exitCode = commandLine.execute(testCheckpointPath);
                assertThat(exitCode).isEqualTo(0);
            });

            assertOutputContains(output, "=== Operators ===");
        }
    }

    @Test
    void testErrorHandling() throws Exception {
        try (MockedStatic<FlinkCheckpointInspector> mockedInspector = mockStatic(FlinkCheckpointInspector.class)) {
            // Mock an IOException when loading metadata
            mockedInspector.when(() -> FlinkCheckpointInspector.loadMetadata(anyString()))
                    .thenThrow(new IOException("Failed to load checkpoint"));

            OperatorsCommand command = new OperatorsCommand();
            java.lang.reflect.Field pathField = OperatorsCommand.class.getDeclaredField("checkpointPath");
            pathField.setAccessible(true);
            pathField.set(command, testCheckpointPath);

            Exception exception = org.junit.jupiter.api.Assertions.assertThrows(
                    Exception.class, command::call);
            assertThat(exception).isInstanceOf(IOException.class);
            assertThat(exception.getMessage()).contains("Failed to load checkpoint");
        }
    }

    @Test
    void testWithNoOperators() throws Exception {
        try (MockedStatic<FlinkCheckpointInspector> mockedInspector = mockStatic(FlinkCheckpointInspector.class)) {
            // Create metadata with no operators
            CheckpointMetadata emptyMetadata = new CheckpointMetadata(
                    123L,
                    Collections.emptyList(),
                    Collections.emptyList(),
                    mockMetadata.getCheckpointProperties());

            mockedInspector.when(() -> FlinkCheckpointInspector.loadMetadata(anyString()))
                    .thenReturn(emptyMetadata);

            OperatorsCommand command = new OperatorsCommand();
            java.lang.reflect.Field pathField = OperatorsCommand.class.getDeclaredField("checkpointPath");
            pathField.setAccessible(true);
            pathField.set(command, testCheckpointPath);

            String output = captureOutput(() -> {
                try {
                    Integer result = command.call();
                    assertThat(result).isEqualTo(0);
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });

            assertOutputContains(output, "=== Operators ===");
            // Should not contain any operator entries
            assertOutputDoesNotContain(output, "Operator 0:");
        }
    }

    @Test
    void testOperatorWithCoordinatorState() throws Exception {
        // Create an operator state with coordinator state
        OperatorState operatorWithCoordinator = TestUtils.createMockOperatorState(
                new org.apache.flink.runtime.jobgraph.OperatorID(), 1, 128);
        
        // We can't easily mock the coordinator state without more complex setup,
        // so we'll test the basic functionality
        OperatorsCommand command = new OperatorsCommand();
        java.lang.reflect.Field pathField = OperatorsCommand.class.getDeclaredField("checkpointPath");
        pathField.setAccessible(true);
        pathField.set(command, testCheckpointPath);

        try (MockedStatic<FlinkCheckpointInspector> mockedStatic = mockStatic(FlinkCheckpointInspector.class)) {
            mockedStatic.when(() -> FlinkCheckpointInspector.loadMetadata(anyString()))
                    .thenReturn(mockMetadata);
            mockedStatic.when(() -> FlinkCheckpointInspector.printSubtaskStateInfo(anyInt(), any()))
                    .thenAnswer(invocation -> {
                        System.out.println("    Mock subtask info");
                        return null;
                    });

            String output = captureOutput(() -> {
                try {
                    command.call();
                } catch (Exception e) {
                    throw new RuntimeException(e);
                }
            });

            assertOutputContains(output, "=== Operators ===");
            assertOutputContains(output, "Parallelism:");
            assertOutputContains(output, "Max Parallelism:");
        }
    }

    @Test
    void testCommandAnnotations() {
        // Verify the command is properly annotated
        CommandLine.Command annotation = OperatorsCommand.class.getAnnotation(CommandLine.Command.class);
        assertThat(annotation).isNotNull();
        assertThat(annotation.name()).isEqualTo("operators");
        assertThat(annotation.description()).containsExactly("List all operators and their state");
    }

    @Test
    void testParameterAnnotations() throws Exception {
        // Verify the checkpoint path parameter is properly annotated
        java.lang.reflect.Field pathField = OperatorsCommand.class.getDeclaredField("checkpointPath");
        CommandLine.Parameters annotation = pathField.getAnnotation(CommandLine.Parameters.class);
        assertThat(annotation).isNotNull();
        assertThat(annotation.index()).isEqualTo("0");
        assertThat(annotation.description()).containsExactly("The path to the checkpoint.");
    }
}
