/*
 * Licensed to the Apache Software Foundation (ASF) under one
 * or more contributor license agreements.  See the NOTICE file
 * distributed with this work for additional information
 * regarding copyright ownership.  The ASF licenses this file
 * to you under the Apache License, Version 2.0 (the
 * "License"); you may not use this file except in compliance
 * with the License.  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package org.apache.flink.tools.checkpoint;

import org.apache.flink.runtime.checkpoint.MasterState;
import org.apache.flink.runtime.checkpoint.OperatorState;
import org.apache.flink.runtime.checkpoint.OperatorSubtaskState;
import org.apache.flink.runtime.checkpoint.metadata.CheckpointMetadata;
import org.apache.flink.runtime.checkpoint.CheckpointProperties;
import org.apache.flink.runtime.checkpoint.CheckpointRetentionPolicy;
import org.apache.flink.runtime.state.StateHandleID;
import org.apache.flink.runtime.state.SharedStateRegistry;
import org.apache.flink.core.fs.FSDataInputStream;
import org.apache.flink.runtime.jobgraph.OperatorID;
import org.apache.flink.runtime.state.KeyGroupRange;
import org.apache.flink.runtime.state.KeyGroupRangeOffsets;
import org.apache.flink.runtime.state.KeyGroupsStateHandle;
import org.apache.flink.runtime.state.KeyedStateHandle;
import org.apache.flink.runtime.state.OperatorStateHandle;
import org.apache.flink.runtime.state.OperatorStreamStateHandle;
import org.apache.flink.runtime.state.StreamStateHandle;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.PrintStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * Utility class for creating test data and mocks for checkpoint inspector tests.
 */
public class TestUtils {

    /**
     * Creates a mock CheckpointMetadata with test data.
     */
    public static CheckpointMetadata createMockCheckpointMetadata() {
        return createMockCheckpointMetadata(123L, 2);
    }

    /**
     * Creates a mock CheckpointMetadata with specified checkpoint ID and number of operators.
     */
    public static CheckpointMetadata createMockCheckpointMetadata(long checkpointId, int numOperators) {
        List<OperatorState> operatorStates = new ArrayList<>();
        
        for (int i = 0; i < numOperators; i++) {
            OperatorID operatorId = new OperatorID();
            OperatorState operatorState = createMockOperatorState(operatorId, 2, 128);
            operatorStates.add(operatorState);
        }

        List<MasterState> masterStates = new ArrayList<>();
        masterStates.add(new MasterState("test-master-state", new byte[]{1, 2, 3, 4}, 1));

        CheckpointProperties properties = CheckpointProperties.forCheckpoint(
            CheckpointRetentionPolicy.NEVER_RETAIN_AFTER_TERMINATION);

        return new CheckpointMetadata(checkpointId, operatorStates, masterStates, properties);
    }

    /**
     * Creates a mock OperatorState with test data.
     */
    public static OperatorState createMockOperatorState(OperatorID operatorId, int parallelism, int maxParallelism) {
        Map<Integer, OperatorSubtaskState> subtaskStates = new HashMap<>();
        
        for (int i = 0; i < parallelism; i++) {
            OperatorSubtaskState subtaskState = createMockOperatorSubtaskState(i, maxParallelism);
            subtaskStates.put(i, subtaskState);
        }

        OperatorState operatorState = new OperatorState(operatorId, parallelism, maxParallelism);
        for (Map.Entry<Integer, OperatorSubtaskState> entry : subtaskStates.entrySet()) {
            operatorState.putState(entry.getKey(), entry.getValue());
        }
        return operatorState;
    }

    /**
     * Creates a mock OperatorSubtaskState with test data.
     */
    public static OperatorSubtaskState createMockOperatorSubtaskState(int subtaskIndex, int maxParallelism) {
        // Create mock keyed state handles
        List<KeyedStateHandle> managedKeyedState = new ArrayList<>();
        KeyGroupRange keyGroupRange = KeyGroupRange.of(
            subtaskIndex * (maxParallelism / 2), 
            (subtaskIndex + 1) * (maxParallelism / 2) - 1);
        
        KeyedStateHandle keyedHandle = createMockKeyGroupsStateHandle(keyGroupRange, 1024);
        managedKeyedState.add(keyedHandle);

        // Create mock operator state handles
        List<OperatorStateHandle> managedOperatorState = new ArrayList<>();
        OperatorStateHandle operatorHandle = createMockOperatorStateHandle();
        managedOperatorState.add(operatorHandle);

        return OperatorSubtaskState.builder()
            .setManagedKeyedState(new org.apache.flink.runtime.checkpoint.StateObjectCollection<>(managedKeyedState))
            .setRawKeyedState(org.apache.flink.runtime.checkpoint.StateObjectCollection.empty())
            .setManagedOperatorState(new org.apache.flink.runtime.checkpoint.StateObjectCollection<>(managedOperatorState))
            .setRawOperatorState(org.apache.flink.runtime.checkpoint.StateObjectCollection.empty())
            .build();
    }

    /**
     * Creates a mock OperatorStateHandle.
     */
    public static OperatorStateHandle createMockOperatorStateHandle() {
        Map<String, OperatorStateHandle.StateMetaInfo> stateNameToPartitionOffsets = new HashMap<>();
        
        // Add some test state names
        stateNameToPartitionOffsets.put("test-state-1", 
            new OperatorStateHandle.StateMetaInfo(new long[]{0, 100}, 
                OperatorStateHandle.Mode.SPLIT_DISTRIBUTE));
        stateNameToPartitionOffsets.put("test-state-2", 
            new OperatorStateHandle.StateMetaInfo(new long[]{200, 300}, 
                OperatorStateHandle.Mode.UNION));

        return new OperatorStreamStateHandle(
            stateNameToPartitionOffsets,
            new MockStreamStateHandle(512));
    }

    /**
     * Creates a mock KeyGroupsStateHandle for testing.
     */
    public static KeyGroupsStateHandle createMockKeyGroupsStateHandle(KeyGroupRange keyGroupRange, long stateSize) {
        // Create a mock StreamStateHandle
        StreamStateHandle mockStreamHandle = new MockStreamStateHandle(stateSize);

        // Create KeyGroupRangeOffsets
        KeyGroupRangeOffsets offsets = new KeyGroupRangeOffsets(keyGroupRange);

        // Return actual KeyGroupsStateHandle instance
        return new KeyGroupsStateHandle(offsets, mockStreamHandle);
    }



    /**
     * Mock implementation of StreamStateHandle for testing.
     */
    public static class MockStreamStateHandle implements StreamStateHandle {
        private final long stateSize;

        public MockStreamStateHandle(long stateSize) {
            this.stateSize = stateSize;
        }

        @Override
        public long getStateSize() {
            return stateSize;
        }

        @Override
        public void discardState() throws Exception {
            // No-op for testing
        }

        @Override
        public org.apache.flink.runtime.state.PhysicalStateHandleID getStreamStateHandleID() {
            return new org.apache.flink.runtime.state.PhysicalStateHandleID("mock-stream-handle-" + stateSize);
        }

        @Override
        public FSDataInputStream openInputStream() throws IOException {
            throw new UnsupportedOperationException("Mock implementation");
        }

        @Override
        public Optional<byte[]> asBytesIfInMemory() {
            return Optional.empty();
        }
    }

    /**
     * Captures System.out output for testing.
     */
    public static class OutputCapture {
        private final PrintStream originalOut;
        private final ByteArrayOutputStream capturedOutput;
        private final PrintStream captureStream;

        public OutputCapture() {
            originalOut = System.out;
            capturedOutput = new ByteArrayOutputStream();
            captureStream = new PrintStream(capturedOutput);
        }

        public void start() {
            System.setOut(captureStream);
        }

        public String stop() {
            System.setOut(originalOut);
            return capturedOutput.toString();
        }

        public String getOutput() {
            return capturedOutput.toString();
        }
    }

    /**
     * Creates a temporary directory for testing.
     */
    public static Path createTempDirectory() throws IOException {
        return Files.createTempDirectory("flink-checkpoint-inspector-test");
    }

    /**
     * Creates a temporary file with specified content.
     */
    public static Path createTempFile(String content) throws IOException {
        Path tempFile = Files.createTempFile("test-checkpoint", ".tmp");
        Files.write(tempFile, content.getBytes());
        return tempFile;
    }

    /**
     * Deletes a file or directory recursively.
     */
    public static void deleteRecursively(Path path) throws IOException {
        if (Files.exists(path)) {
            if (Files.isDirectory(path)) {
                Files.walk(path)
                    .sorted((a, b) -> b.compareTo(a)) // Delete files before directories
                    .forEach(p -> {
                        try {
                            Files.delete(p);
                        } catch (IOException e) {
                            // Ignore for cleanup
                        }
                    });
            } else {
                Files.delete(path);
            }
        }
    }

    /**
     * Normalizes line endings in output for cross-platform testing.
     */
    public static String normalizeLineEndings(String text) {
        return text.replace("\r\n", "\n").replace("\r", "\n");
    }
}
