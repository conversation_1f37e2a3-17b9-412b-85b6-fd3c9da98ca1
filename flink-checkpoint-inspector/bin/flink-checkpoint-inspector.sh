#!/bin/bash

################################################################################
#  Licensed to the Apache Software Foundation (ASF) under one
#  or more contributor license agreements.  See the NOTICE file
#  distributed with this work for additional information
#  regarding copyright ownership.  The ASF licenses this file
#  to you under the Apache License, Version 2.0 (the
#  "License"); you may not use this file except in compliance
#  with the License.  You may obtain a copy of the License at
#
#      http://www.apache.org/licenses/LICENSE-2.0
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
################################################################################

# Flink Checkpoint Inspector startup script

# Resolve the directory of this script
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
PROJECT_DIR="$(dirname "$SCRIPT_DIR")"

# Find the JAR file
JAR_FILE=$(find "$PROJECT_DIR/target" -name "flink-checkpoint-inspector-*.jar" | head -1)

if [ -z "$JAR_FILE" ]; then
    echo "Error: JAR file not found. Please build the project first:"
    echo "  cd $PROJECT_DIR"
    echo "  mvn clean package"
    exit 1
fi

# Check if Java is available
if ! command -v java &> /dev/null; then
    echo "Error: Java is not installed or not in PATH"
    exit 1
fi

# Set JVM options
JVM_OPTS=${JVM_OPTS:-"-Xmx2g"}

# Run the tool
exec java $JVM_OPTS -jar "$JAR_FILE" "$@"
