# This is a RocksDB option file.
#
# For detailed file format spec, please refer to the example file
# in examples/rocksdb_option_file_example.ini
#

[Version]
  rocksdb_version=6.20.3
  options_file_version=1.1

[DBOptions]
  max_background_flushes=-1
  compaction_readahead_size=0
  strict_bytes_per_sync=false
  wal_bytes_per_sync=0
  max_open_files=-1
  stats_history_buffer_size=1048576
  max_total_wal_size=0
  stats_persist_period_sec=600
  stats_dump_period_sec=0
  avoid_flush_during_shutdown=false
  max_subcompactions=1
  bytes_per_sync=0
  delayed_write_rate=16777216
  max_background_compactions=-1
  max_background_jobs=2
  delete_obsolete_files_period_micros=21600000000
  writable_file_max_buffer_size=1048576
  base_background_compactions=-1
  allow_data_in_errors=false
  max_bgerror_resume_count=2147483647
  best_efforts_recovery=false
  write_dbid_to_manifest=false
  atomic_flush=false
  manual_wal_flush=false
  two_write_queues=false
  preserve_deletes=false
  avoid_flush_during_recovery=false
  dump_malloc_stats=false
  info_log_level=INFO_LEVEL
  access_hint_on_compaction_start=NORMAL
  use_adaptive_mutex=false
  max_write_batch_group_size_bytes=1048576
  wal_recovery_mode=kPointInTimeRecovery
  bgerror_resume_retry_interval=1000000
  paranoid_checks=true
  WAL_size_limit_MB=0
  allow_concurrent_memtable_write=true
  allow_ingest_behind=false
  fail_if_options_file_error=false
  persist_stats_to_disk=false
  WAL_ttl_seconds=0
  wal_dir=/var/folders/52/5ypclyt917l8h1pfdrt_f8lw0000gp/T/junit3532207755598134463/junit1417679891574706949/minicluster_5dd0dd5d720cd0bbc72fc154435c2e13/tm_0/tmp/job_2991ea4a6c36d90648ac4da35f0f00fb_op_StreamFlatMap_d1392353922252257afa15351a98bae9__1_4__uuid_6525d863-676f-4d54-b114-c2423346ab77/db
  keep_log_file_num=4
  table_cache_numshardbits=6
  max_file_opening_threads=16
  use_fsync=false
  unordered_write=false
  random_access_max_buffer_size=1048576
  db_write_buffer_size=0
  allow_2pc=false
  skip_checking_sst_file_sizes_on_db_open=false
  write_thread_slow_yield_usec=3
  skip_stats_update_on_db_open=false
  track_and_verify_wals_in_manifest=false
  error_if_exists=false
  manifest_preallocation_size=4194304
  is_fd_close_on_exec=true
  max_log_file_size=26214400
  advise_random_on_open=true
  use_direct_reads=false
  write_thread_max_yield_usec=100
  enable_write_thread_adaptive_yield=true
  create_missing_column_families=false
  create_if_missing=true
  log_file_time_to_roll=0
  use_direct_io_for_flush_and_compaction=false
  avoid_unnecessary_blocking_io=false
  allow_fallocate=true
  max_manifest_file_size=1073741824
  enable_thread_tracking=false
  recycle_log_file_num=0
  db_host_id=__hostname__
  allow_mmap_reads=false
  log_readahead_size=0
  enable_pipelined_write=false
  new_table_reader_for_compaction_inputs=false
  allow_mmap_writes=false
  

[CFOptions "default"]
  bottommost_compression=kDisableCompressionOption
  sample_for_compression=0
  blob_garbage_collection_age_cutoff=0.250000
  arena_block_size=8388608
  enable_blob_garbage_collection=false
  level0_stop_writes_trigger=36
  min_blob_size=0
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;min_merge_width=2;compression_size_percent=-1;max_size_amplification_percent=200;max_merge_width=4294967295;size_ratio=1;}
  target_file_size_base=67108864
  max_bytes_for_level_base=268435456
  memtable_whole_key_filtering=false
  soft_pending_compaction_bytes_limit=68719476736
  blob_compression_type=kNoCompression
  max_write_buffer_number=2
  ttl=2592000
  compaction_options_fifo={allow_compaction=false;max_table_files_size=1073741824;}
  check_flush_compaction_key_order=true
  max_successive_merges=0
  inplace_update_num_locks=10000
  bottommost_compression_opts={enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  target_file_size_multiplier=1
  max_bytes_for_level_multiplier_additional=1:{1}:{1}:{1}:{1}:{1}:{1}
  enable_blob_files=false
  level0_slowdown_writes_trigger=20
  compression=kSnappyCompression
  level0_file_num_compaction_trigger=4
  blob_file_size=268435456
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier=10.000000
  write_buffer_size=67108864
  disable_auto_compactions=false
  max_compaction_bytes=1677721600
  memtable_huge_page_size=0
  compression_opts={enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  hard_pending_compaction_bytes_limit=274877906944
  periodic_compaction_seconds=0
  paranoid_file_checks=false
  memtable_prefix_bloom_size_ratio=0.000000
  max_sequential_skip_in_iterations=8
  report_bg_io_stats=false
  compaction_pri=kMinOverlappingRatio
  compaction_style=kCompactionStyleLevel
  memtable_factory=SkipListFactory
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  compaction_filter_factory=nullptr
  min_write_buffer_number_to_merge=1
  max_write_buffer_number_to_maintain=0
  compaction_filter=nullptr
  merge_operator=StringAppendTESTOperator
  num_levels=7
  optimize_filters_for_hits=false
  force_consistency_checks=true
  table_factory=BlockBasedTable
  max_write_buffer_size_to_maintain=0
  memtable_insert_with_hint_prefix_extractor=nullptr
  level_compaction_dynamic_level_bytes=false
  inplace_update_support=false
  
[TableOptions/BlockBasedTable "default"]
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  read_amp_bytes_per_bit=0
  verify_compression=false
  format_version=5
  optimize_filters_for_memory=false
  partition_filters=false
  max_auto_readahead_size=262144
  enable_index_compression=true
  checksum=kCRC32c
  index_block_restart_interval=1
  pin_top_level_index_and_filter=true
  block_align=false
  block_size=4096
  index_type=kBinarySearch
  filter_policy=nullptr
  metadata_block_size=4096
  no_block_cache=false
  whole_key_filtering=true
  index_shortening=kShortenSeparators
  block_size_deviation=10
  data_block_index_type=kDataBlockBinarySearch
  data_block_hash_table_util_ratio=0.750000
  cache_index_and_filter_blocks=true
  block_restart_interval=16
  pin_l0_filter_and_index_blocks_in_cache=true
  hash_index_allow_collision=true
  cache_index_and_filter_blocks_with_high_priority=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  

[CFOptions "caseClassState"]
  bottommost_compression=kDisableCompressionOption
  sample_for_compression=0
  blob_garbage_collection_age_cutoff=0.250000
  arena_block_size=8388608
  enable_blob_garbage_collection=false
  level0_stop_writes_trigger=36
  min_blob_size=0
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;min_merge_width=2;compression_size_percent=-1;max_size_amplification_percent=200;max_merge_width=4294967295;size_ratio=1;}
  target_file_size_base=67108864
  max_bytes_for_level_base=268435456
  memtable_whole_key_filtering=false
  soft_pending_compaction_bytes_limit=68719476736
  blob_compression_type=kNoCompression
  max_write_buffer_number=2
  ttl=2592000
  compaction_options_fifo={allow_compaction=false;max_table_files_size=1073741824;}
  check_flush_compaction_key_order=true
  max_successive_merges=0
  inplace_update_num_locks=10000
  bottommost_compression_opts={enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  target_file_size_multiplier=1
  max_bytes_for_level_multiplier_additional=1:{1}:{1}:{1}:{1}:{1}:{1}
  enable_blob_files=false
  level0_slowdown_writes_trigger=20
  compression=kSnappyCompression
  level0_file_num_compaction_trigger=4
  blob_file_size=268435456
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier=10.000000
  write_buffer_size=67108864
  disable_auto_compactions=false
  max_compaction_bytes=1677721600
  memtable_huge_page_size=0
  compression_opts={enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  hard_pending_compaction_bytes_limit=274877906944
  periodic_compaction_seconds=0
  paranoid_file_checks=false
  memtable_prefix_bloom_size_ratio=0.000000
  max_sequential_skip_in_iterations=8
  report_bg_io_stats=false
  compaction_pri=kMinOverlappingRatio
  compaction_style=kCompactionStyleLevel
  memtable_factory=SkipListFactory
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  compaction_filter_factory=nullptr
  min_write_buffer_number_to_merge=1
  max_write_buffer_number_to_maintain=0
  compaction_filter=nullptr
  merge_operator=StringAppendTESTOperator
  num_levels=7
  optimize_filters_for_hits=false
  force_consistency_checks=true
  table_factory=BlockBasedTable
  max_write_buffer_size_to_maintain=0
  memtable_insert_with_hint_prefix_extractor=nullptr
  level_compaction_dynamic_level_bytes=false
  inplace_update_support=false
  
[TableOptions/BlockBasedTable "caseClassState"]
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  read_amp_bytes_per_bit=0
  verify_compression=false
  format_version=5
  optimize_filters_for_memory=false
  partition_filters=false
  max_auto_readahead_size=262144
  enable_index_compression=true
  checksum=kCRC32c
  index_block_restart_interval=1
  pin_top_level_index_and_filter=true
  block_align=false
  block_size=4096
  index_type=kBinarySearch
  filter_policy=nullptr
  metadata_block_size=4096
  no_block_cache=false
  whole_key_filtering=true
  index_shortening=kShortenSeparators
  block_size_deviation=10
  data_block_index_type=kDataBlockBinarySearch
  data_block_hash_table_util_ratio=0.750000
  cache_index_and_filter_blocks=true
  block_restart_interval=16
  pin_l0_filter_and_index_blocks_in_cache=true
  hash_index_allow_collision=true
  cache_index_and_filter_blocks_with_high_priority=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  

[CFOptions "caseClassWithNestingState"]
  bottommost_compression=kDisableCompressionOption
  sample_for_compression=0
  blob_garbage_collection_age_cutoff=0.250000
  arena_block_size=8388608
  enable_blob_garbage_collection=false
  level0_stop_writes_trigger=36
  min_blob_size=0
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;min_merge_width=2;compression_size_percent=-1;max_size_amplification_percent=200;max_merge_width=4294967295;size_ratio=1;}
  target_file_size_base=67108864
  max_bytes_for_level_base=268435456
  memtable_whole_key_filtering=false
  soft_pending_compaction_bytes_limit=68719476736
  blob_compression_type=kNoCompression
  max_write_buffer_number=2
  ttl=2592000
  compaction_options_fifo={allow_compaction=false;max_table_files_size=1073741824;}
  check_flush_compaction_key_order=true
  max_successive_merges=0
  inplace_update_num_locks=10000
  bottommost_compression_opts={enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  target_file_size_multiplier=1
  max_bytes_for_level_multiplier_additional=1:{1}:{1}:{1}:{1}:{1}:{1}
  enable_blob_files=false
  level0_slowdown_writes_trigger=20
  compression=kSnappyCompression
  level0_file_num_compaction_trigger=4
  blob_file_size=268435456
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier=10.000000
  write_buffer_size=67108864
  disable_auto_compactions=false
  max_compaction_bytes=1677721600
  memtable_huge_page_size=0
  compression_opts={enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  hard_pending_compaction_bytes_limit=274877906944
  periodic_compaction_seconds=0
  paranoid_file_checks=false
  memtable_prefix_bloom_size_ratio=0.000000
  max_sequential_skip_in_iterations=8
  report_bg_io_stats=false
  compaction_pri=kMinOverlappingRatio
  compaction_style=kCompactionStyleLevel
  memtable_factory=SkipListFactory
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  compaction_filter_factory=nullptr
  min_write_buffer_number_to_merge=1
  max_write_buffer_number_to_maintain=0
  compaction_filter=nullptr
  merge_operator=StringAppendTESTOperator
  num_levels=7
  optimize_filters_for_hits=false
  force_consistency_checks=true
  table_factory=BlockBasedTable
  max_write_buffer_size_to_maintain=0
  memtable_insert_with_hint_prefix_extractor=nullptr
  level_compaction_dynamic_level_bytes=false
  inplace_update_support=false
  
[TableOptions/BlockBasedTable "caseClassWithNestingState"]
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  read_amp_bytes_per_bit=0
  verify_compression=false
  format_version=5
  optimize_filters_for_memory=false
  partition_filters=false
  max_auto_readahead_size=262144
  enable_index_compression=true
  checksum=kCRC32c
  index_block_restart_interval=1
  pin_top_level_index_and_filter=true
  block_align=false
  block_size=4096
  index_type=kBinarySearch
  filter_policy=nullptr
  metadata_block_size=4096
  no_block_cache=false
  whole_key_filtering=true
  index_shortening=kShortenSeparators
  block_size_deviation=10
  data_block_index_type=kDataBlockBinarySearch
  data_block_hash_table_util_ratio=0.750000
  cache_index_and_filter_blocks=true
  block_restart_interval=16
  pin_l0_filter_and_index_blocks_in_cache=true
  hash_index_allow_collision=true
  cache_index_and_filter_blocks_with_high_priority=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  

[CFOptions "collectionState"]
  bottommost_compression=kDisableCompressionOption
  sample_for_compression=0
  blob_garbage_collection_age_cutoff=0.250000
  arena_block_size=8388608
  enable_blob_garbage_collection=false
  level0_stop_writes_trigger=36
  min_blob_size=0
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;min_merge_width=2;compression_size_percent=-1;max_size_amplification_percent=200;max_merge_width=4294967295;size_ratio=1;}
  target_file_size_base=67108864
  max_bytes_for_level_base=268435456
  memtable_whole_key_filtering=false
  soft_pending_compaction_bytes_limit=68719476736
  blob_compression_type=kNoCompression
  max_write_buffer_number=2
  ttl=2592000
  compaction_options_fifo={allow_compaction=false;max_table_files_size=1073741824;}
  check_flush_compaction_key_order=true
  max_successive_merges=0
  inplace_update_num_locks=10000
  bottommost_compression_opts={enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  target_file_size_multiplier=1
  max_bytes_for_level_multiplier_additional=1:{1}:{1}:{1}:{1}:{1}:{1}
  enable_blob_files=false
  level0_slowdown_writes_trigger=20
  compression=kSnappyCompression
  level0_file_num_compaction_trigger=4
  blob_file_size=268435456
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier=10.000000
  write_buffer_size=67108864
  disable_auto_compactions=false
  max_compaction_bytes=1677721600
  memtable_huge_page_size=0
  compression_opts={enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  hard_pending_compaction_bytes_limit=274877906944
  periodic_compaction_seconds=0
  paranoid_file_checks=false
  memtable_prefix_bloom_size_ratio=0.000000
  max_sequential_skip_in_iterations=8
  report_bg_io_stats=false
  compaction_pri=kMinOverlappingRatio
  compaction_style=kCompactionStyleLevel
  memtable_factory=SkipListFactory
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  compaction_filter_factory=nullptr
  min_write_buffer_number_to_merge=1
  max_write_buffer_number_to_maintain=0
  compaction_filter=nullptr
  merge_operator=StringAppendTESTOperator
  num_levels=7
  optimize_filters_for_hits=false
  force_consistency_checks=true
  table_factory=BlockBasedTable
  max_write_buffer_size_to_maintain=0
  memtable_insert_with_hint_prefix_extractor=nullptr
  level_compaction_dynamic_level_bytes=false
  inplace_update_support=false
  
[TableOptions/BlockBasedTable "collectionState"]
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  read_amp_bytes_per_bit=0
  verify_compression=false
  format_version=5
  optimize_filters_for_memory=false
  partition_filters=false
  max_auto_readahead_size=262144
  enable_index_compression=true
  checksum=kCRC32c
  index_block_restart_interval=1
  pin_top_level_index_and_filter=true
  block_align=false
  block_size=4096
  index_type=kBinarySearch
  filter_policy=nullptr
  metadata_block_size=4096
  no_block_cache=false
  whole_key_filtering=true
  index_shortening=kShortenSeparators
  block_size_deviation=10
  data_block_index_type=kDataBlockBinarySearch
  data_block_hash_table_util_ratio=0.750000
  cache_index_and_filter_blocks=true
  block_restart_interval=16
  pin_l0_filter_and_index_blocks_in_cache=true
  hash_index_allow_collision=true
  cache_index_and_filter_blocks_with_high_priority=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  

[CFOptions "tryState"]
  bottommost_compression=kDisableCompressionOption
  sample_for_compression=0
  blob_garbage_collection_age_cutoff=0.250000
  arena_block_size=8388608
  enable_blob_garbage_collection=false
  level0_stop_writes_trigger=36
  min_blob_size=0
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;min_merge_width=2;compression_size_percent=-1;max_size_amplification_percent=200;max_merge_width=4294967295;size_ratio=1;}
  target_file_size_base=67108864
  max_bytes_for_level_base=268435456
  memtable_whole_key_filtering=false
  soft_pending_compaction_bytes_limit=68719476736
  blob_compression_type=kNoCompression
  max_write_buffer_number=2
  ttl=2592000
  compaction_options_fifo={allow_compaction=false;max_table_files_size=1073741824;}
  check_flush_compaction_key_order=true
  max_successive_merges=0
  inplace_update_num_locks=10000
  bottommost_compression_opts={enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  target_file_size_multiplier=1
  max_bytes_for_level_multiplier_additional=1:{1}:{1}:{1}:{1}:{1}:{1}
  enable_blob_files=false
  level0_slowdown_writes_trigger=20
  compression=kSnappyCompression
  level0_file_num_compaction_trigger=4
  blob_file_size=268435456
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier=10.000000
  write_buffer_size=67108864
  disable_auto_compactions=false
  max_compaction_bytes=1677721600
  memtable_huge_page_size=0
  compression_opts={enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  hard_pending_compaction_bytes_limit=274877906944
  periodic_compaction_seconds=0
  paranoid_file_checks=false
  memtable_prefix_bloom_size_ratio=0.000000
  max_sequential_skip_in_iterations=8
  report_bg_io_stats=false
  compaction_pri=kMinOverlappingRatio
  compaction_style=kCompactionStyleLevel
  memtable_factory=SkipListFactory
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  compaction_filter_factory=nullptr
  min_write_buffer_number_to_merge=1
  max_write_buffer_number_to_maintain=0
  compaction_filter=nullptr
  merge_operator=StringAppendTESTOperator
  num_levels=7
  optimize_filters_for_hits=false
  force_consistency_checks=true
  table_factory=BlockBasedTable
  max_write_buffer_size_to_maintain=0
  memtable_insert_with_hint_prefix_extractor=nullptr
  level_compaction_dynamic_level_bytes=false
  inplace_update_support=false
  
[TableOptions/BlockBasedTable "tryState"]
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  read_amp_bytes_per_bit=0
  verify_compression=false
  format_version=5
  optimize_filters_for_memory=false
  partition_filters=false
  max_auto_readahead_size=262144
  enable_index_compression=true
  checksum=kCRC32c
  index_block_restart_interval=1
  pin_top_level_index_and_filter=true
  block_align=false
  block_size=4096
  index_type=kBinarySearch
  filter_policy=nullptr
  metadata_block_size=4096
  no_block_cache=false
  whole_key_filtering=true
  index_shortening=kShortenSeparators
  block_size_deviation=10
  data_block_index_type=kDataBlockBinarySearch
  data_block_hash_table_util_ratio=0.750000
  cache_index_and_filter_blocks=true
  block_restart_interval=16
  pin_l0_filter_and_index_blocks_in_cache=true
  hash_index_allow_collision=true
  cache_index_and_filter_blocks_with_high_priority=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  

[CFOptions "tryFailureState"]
  bottommost_compression=kDisableCompressionOption
  sample_for_compression=0
  blob_garbage_collection_age_cutoff=0.250000
  arena_block_size=8388608
  enable_blob_garbage_collection=false
  level0_stop_writes_trigger=36
  min_blob_size=0
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;min_merge_width=2;compression_size_percent=-1;max_size_amplification_percent=200;max_merge_width=4294967295;size_ratio=1;}
  target_file_size_base=67108864
  max_bytes_for_level_base=268435456
  memtable_whole_key_filtering=false
  soft_pending_compaction_bytes_limit=68719476736
  blob_compression_type=kNoCompression
  max_write_buffer_number=2
  ttl=2592000
  compaction_options_fifo={allow_compaction=false;max_table_files_size=1073741824;}
  check_flush_compaction_key_order=true
  max_successive_merges=0
  inplace_update_num_locks=10000
  bottommost_compression_opts={enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  target_file_size_multiplier=1
  max_bytes_for_level_multiplier_additional=1:{1}:{1}:{1}:{1}:{1}:{1}
  enable_blob_files=false
  level0_slowdown_writes_trigger=20
  compression=kSnappyCompression
  level0_file_num_compaction_trigger=4
  blob_file_size=268435456
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier=10.000000
  write_buffer_size=67108864
  disable_auto_compactions=false
  max_compaction_bytes=1677721600
  memtable_huge_page_size=0
  compression_opts={enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  hard_pending_compaction_bytes_limit=274877906944
  periodic_compaction_seconds=0
  paranoid_file_checks=false
  memtable_prefix_bloom_size_ratio=0.000000
  max_sequential_skip_in_iterations=8
  report_bg_io_stats=false
  compaction_pri=kMinOverlappingRatio
  compaction_style=kCompactionStyleLevel
  memtable_factory=SkipListFactory
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  compaction_filter_factory=nullptr
  min_write_buffer_number_to_merge=1
  max_write_buffer_number_to_maintain=0
  compaction_filter=nullptr
  merge_operator=StringAppendTESTOperator
  num_levels=7
  optimize_filters_for_hits=false
  force_consistency_checks=true
  table_factory=BlockBasedTable
  max_write_buffer_size_to_maintain=0
  memtable_insert_with_hint_prefix_extractor=nullptr
  level_compaction_dynamic_level_bytes=false
  inplace_update_support=false
  
[TableOptions/BlockBasedTable "tryFailureState"]
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  read_amp_bytes_per_bit=0
  verify_compression=false
  format_version=5
  optimize_filters_for_memory=false
  partition_filters=false
  max_auto_readahead_size=262144
  enable_index_compression=true
  checksum=kCRC32c
  index_block_restart_interval=1
  pin_top_level_index_and_filter=true
  block_align=false
  block_size=4096
  index_type=kBinarySearch
  filter_policy=nullptr
  metadata_block_size=4096
  no_block_cache=false
  whole_key_filtering=true
  index_shortening=kShortenSeparators
  block_size_deviation=10
  data_block_index_type=kDataBlockBinarySearch
  data_block_hash_table_util_ratio=0.750000
  cache_index_and_filter_blocks=true
  block_restart_interval=16
  pin_l0_filter_and_index_blocks_in_cache=true
  hash_index_allow_collision=true
  cache_index_and_filter_blocks_with_high_priority=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  

[CFOptions "optionState"]
  bottommost_compression=kDisableCompressionOption
  sample_for_compression=0
  blob_garbage_collection_age_cutoff=0.250000
  arena_block_size=8388608
  enable_blob_garbage_collection=false
  level0_stop_writes_trigger=36
  min_blob_size=0
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;min_merge_width=2;compression_size_percent=-1;max_size_amplification_percent=200;max_merge_width=4294967295;size_ratio=1;}
  target_file_size_base=67108864
  max_bytes_for_level_base=268435456
  memtable_whole_key_filtering=false
  soft_pending_compaction_bytes_limit=68719476736
  blob_compression_type=kNoCompression
  max_write_buffer_number=2
  ttl=2592000
  compaction_options_fifo={allow_compaction=false;max_table_files_size=1073741824;}
  check_flush_compaction_key_order=true
  max_successive_merges=0
  inplace_update_num_locks=10000
  bottommost_compression_opts={enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  target_file_size_multiplier=1
  max_bytes_for_level_multiplier_additional=1:{1}:{1}:{1}:{1}:{1}:{1}
  enable_blob_files=false
  level0_slowdown_writes_trigger=20
  compression=kSnappyCompression
  level0_file_num_compaction_trigger=4
  blob_file_size=268435456
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier=10.000000
  write_buffer_size=67108864
  disable_auto_compactions=false
  max_compaction_bytes=1677721600
  memtable_huge_page_size=0
  compression_opts={enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  hard_pending_compaction_bytes_limit=274877906944
  periodic_compaction_seconds=0
  paranoid_file_checks=false
  memtable_prefix_bloom_size_ratio=0.000000
  max_sequential_skip_in_iterations=8
  report_bg_io_stats=false
  compaction_pri=kMinOverlappingRatio
  compaction_style=kCompactionStyleLevel
  memtable_factory=SkipListFactory
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  compaction_filter_factory=nullptr
  min_write_buffer_number_to_merge=1
  max_write_buffer_number_to_maintain=0
  compaction_filter=nullptr
  merge_operator=StringAppendTESTOperator
  num_levels=7
  optimize_filters_for_hits=false
  force_consistency_checks=true
  table_factory=BlockBasedTable
  max_write_buffer_size_to_maintain=0
  memtable_insert_with_hint_prefix_extractor=nullptr
  level_compaction_dynamic_level_bytes=false
  inplace_update_support=false
  
[TableOptions/BlockBasedTable "optionState"]
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  read_amp_bytes_per_bit=0
  verify_compression=false
  format_version=5
  optimize_filters_for_memory=false
  partition_filters=false
  max_auto_readahead_size=262144
  enable_index_compression=true
  checksum=kCRC32c
  index_block_restart_interval=1
  pin_top_level_index_and_filter=true
  block_align=false
  block_size=4096
  index_type=kBinarySearch
  filter_policy=nullptr
  metadata_block_size=4096
  no_block_cache=false
  whole_key_filtering=true
  index_shortening=kShortenSeparators
  block_size_deviation=10
  data_block_index_type=kDataBlockBinarySearch
  data_block_hash_table_util_ratio=0.750000
  cache_index_and_filter_blocks=true
  block_restart_interval=16
  pin_l0_filter_and_index_blocks_in_cache=true
  hash_index_allow_collision=true
  cache_index_and_filter_blocks_with_high_priority=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  

[CFOptions "optionNoneState"]
  bottommost_compression=kDisableCompressionOption
  sample_for_compression=0
  blob_garbage_collection_age_cutoff=0.250000
  arena_block_size=8388608
  enable_blob_garbage_collection=false
  level0_stop_writes_trigger=36
  min_blob_size=0
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;min_merge_width=2;compression_size_percent=-1;max_size_amplification_percent=200;max_merge_width=4294967295;size_ratio=1;}
  target_file_size_base=67108864
  max_bytes_for_level_base=268435456
  memtable_whole_key_filtering=false
  soft_pending_compaction_bytes_limit=68719476736
  blob_compression_type=kNoCompression
  max_write_buffer_number=2
  ttl=2592000
  compaction_options_fifo={allow_compaction=false;max_table_files_size=1073741824;}
  check_flush_compaction_key_order=true
  max_successive_merges=0
  inplace_update_num_locks=10000
  bottommost_compression_opts={enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  target_file_size_multiplier=1
  max_bytes_for_level_multiplier_additional=1:{1}:{1}:{1}:{1}:{1}:{1}
  enable_blob_files=false
  level0_slowdown_writes_trigger=20
  compression=kSnappyCompression
  level0_file_num_compaction_trigger=4
  blob_file_size=268435456
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier=10.000000
  write_buffer_size=67108864
  disable_auto_compactions=false
  max_compaction_bytes=1677721600
  memtable_huge_page_size=0
  compression_opts={enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  hard_pending_compaction_bytes_limit=274877906944
  periodic_compaction_seconds=0
  paranoid_file_checks=false
  memtable_prefix_bloom_size_ratio=0.000000
  max_sequential_skip_in_iterations=8
  report_bg_io_stats=false
  compaction_pri=kMinOverlappingRatio
  compaction_style=kCompactionStyleLevel
  memtable_factory=SkipListFactory
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  compaction_filter_factory=nullptr
  min_write_buffer_number_to_merge=1
  max_write_buffer_number_to_maintain=0
  compaction_filter=nullptr
  merge_operator=StringAppendTESTOperator
  num_levels=7
  optimize_filters_for_hits=false
  force_consistency_checks=true
  table_factory=BlockBasedTable
  max_write_buffer_size_to_maintain=0
  memtable_insert_with_hint_prefix_extractor=nullptr
  level_compaction_dynamic_level_bytes=false
  inplace_update_support=false
  
[TableOptions/BlockBasedTable "optionNoneState"]
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  read_amp_bytes_per_bit=0
  verify_compression=false
  format_version=5
  optimize_filters_for_memory=false
  partition_filters=false
  max_auto_readahead_size=262144
  enable_index_compression=true
  checksum=kCRC32c
  index_block_restart_interval=1
  pin_top_level_index_and_filter=true
  block_align=false
  block_size=4096
  index_type=kBinarySearch
  filter_policy=nullptr
  metadata_block_size=4096
  no_block_cache=false
  whole_key_filtering=true
  index_shortening=kShortenSeparators
  block_size_deviation=10
  data_block_index_type=kDataBlockBinarySearch
  data_block_hash_table_util_ratio=0.750000
  cache_index_and_filter_blocks=true
  block_restart_interval=16
  pin_l0_filter_and_index_blocks_in_cache=true
  hash_index_allow_collision=true
  cache_index_and_filter_blocks_with_high_priority=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  

[CFOptions "eitherLeftState"]
  bottommost_compression=kDisableCompressionOption
  sample_for_compression=0
  blob_garbage_collection_age_cutoff=0.250000
  arena_block_size=8388608
  enable_blob_garbage_collection=false
  level0_stop_writes_trigger=36
  min_blob_size=0
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;min_merge_width=2;compression_size_percent=-1;max_size_amplification_percent=200;max_merge_width=4294967295;size_ratio=1;}
  target_file_size_base=67108864
  max_bytes_for_level_base=268435456
  memtable_whole_key_filtering=false
  soft_pending_compaction_bytes_limit=68719476736
  blob_compression_type=kNoCompression
  max_write_buffer_number=2
  ttl=2592000
  compaction_options_fifo={allow_compaction=false;max_table_files_size=1073741824;}
  check_flush_compaction_key_order=true
  max_successive_merges=0
  inplace_update_num_locks=10000
  bottommost_compression_opts={enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  target_file_size_multiplier=1
  max_bytes_for_level_multiplier_additional=1:{1}:{1}:{1}:{1}:{1}:{1}
  enable_blob_files=false
  level0_slowdown_writes_trigger=20
  compression=kSnappyCompression
  level0_file_num_compaction_trigger=4
  blob_file_size=268435456
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier=10.000000
  write_buffer_size=67108864
  disable_auto_compactions=false
  max_compaction_bytes=1677721600
  memtable_huge_page_size=0
  compression_opts={enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  hard_pending_compaction_bytes_limit=274877906944
  periodic_compaction_seconds=0
  paranoid_file_checks=false
  memtable_prefix_bloom_size_ratio=0.000000
  max_sequential_skip_in_iterations=8
  report_bg_io_stats=false
  compaction_pri=kMinOverlappingRatio
  compaction_style=kCompactionStyleLevel
  memtable_factory=SkipListFactory
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  compaction_filter_factory=nullptr
  min_write_buffer_number_to_merge=1
  max_write_buffer_number_to_maintain=0
  compaction_filter=nullptr
  merge_operator=StringAppendTESTOperator
  num_levels=7
  optimize_filters_for_hits=false
  force_consistency_checks=true
  table_factory=BlockBasedTable
  max_write_buffer_size_to_maintain=0
  memtable_insert_with_hint_prefix_extractor=nullptr
  level_compaction_dynamic_level_bytes=false
  inplace_update_support=false
  
[TableOptions/BlockBasedTable "eitherLeftState"]
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  read_amp_bytes_per_bit=0
  verify_compression=false
  format_version=5
  optimize_filters_for_memory=false
  partition_filters=false
  max_auto_readahead_size=262144
  enable_index_compression=true
  checksum=kCRC32c
  index_block_restart_interval=1
  pin_top_level_index_and_filter=true
  block_align=false
  block_size=4096
  index_type=kBinarySearch
  filter_policy=nullptr
  metadata_block_size=4096
  no_block_cache=false
  whole_key_filtering=true
  index_shortening=kShortenSeparators
  block_size_deviation=10
  data_block_index_type=kDataBlockBinarySearch
  data_block_hash_table_util_ratio=0.750000
  cache_index_and_filter_blocks=true
  block_restart_interval=16
  pin_l0_filter_and_index_blocks_in_cache=true
  hash_index_allow_collision=true
  cache_index_and_filter_blocks_with_high_priority=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  

[CFOptions "eitherRightState"]
  bottommost_compression=kDisableCompressionOption
  sample_for_compression=0
  blob_garbage_collection_age_cutoff=0.250000
  arena_block_size=8388608
  enable_blob_garbage_collection=false
  level0_stop_writes_trigger=36
  min_blob_size=0
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;min_merge_width=2;compression_size_percent=-1;max_size_amplification_percent=200;max_merge_width=4294967295;size_ratio=1;}
  target_file_size_base=67108864
  max_bytes_for_level_base=268435456
  memtable_whole_key_filtering=false
  soft_pending_compaction_bytes_limit=68719476736
  blob_compression_type=kNoCompression
  max_write_buffer_number=2
  ttl=2592000
  compaction_options_fifo={allow_compaction=false;max_table_files_size=1073741824;}
  check_flush_compaction_key_order=true
  max_successive_merges=0
  inplace_update_num_locks=10000
  bottommost_compression_opts={enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  target_file_size_multiplier=1
  max_bytes_for_level_multiplier_additional=1:{1}:{1}:{1}:{1}:{1}:{1}
  enable_blob_files=false
  level0_slowdown_writes_trigger=20
  compression=kSnappyCompression
  level0_file_num_compaction_trigger=4
  blob_file_size=268435456
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier=10.000000
  write_buffer_size=67108864
  disable_auto_compactions=false
  max_compaction_bytes=1677721600
  memtable_huge_page_size=0
  compression_opts={enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  hard_pending_compaction_bytes_limit=274877906944
  periodic_compaction_seconds=0
  paranoid_file_checks=false
  memtable_prefix_bloom_size_ratio=0.000000
  max_sequential_skip_in_iterations=8
  report_bg_io_stats=false
  compaction_pri=kMinOverlappingRatio
  compaction_style=kCompactionStyleLevel
  memtable_factory=SkipListFactory
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  compaction_filter_factory=nullptr
  min_write_buffer_number_to_merge=1
  max_write_buffer_number_to_maintain=0
  compaction_filter=nullptr
  merge_operator=StringAppendTESTOperator
  num_levels=7
  optimize_filters_for_hits=false
  force_consistency_checks=true
  table_factory=BlockBasedTable
  max_write_buffer_size_to_maintain=0
  memtable_insert_with_hint_prefix_extractor=nullptr
  level_compaction_dynamic_level_bytes=false
  inplace_update_support=false
  
[TableOptions/BlockBasedTable "eitherRightState"]
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  read_amp_bytes_per_bit=0
  verify_compression=false
  format_version=5
  optimize_filters_for_memory=false
  partition_filters=false
  max_auto_readahead_size=262144
  enable_index_compression=true
  checksum=kCRC32c
  index_block_restart_interval=1
  pin_top_level_index_and_filter=true
  block_align=false
  block_size=4096
  index_type=kBinarySearch
  filter_policy=nullptr
  metadata_block_size=4096
  no_block_cache=false
  whole_key_filtering=true
  index_shortening=kShortenSeparators
  block_size_deviation=10
  data_block_index_type=kDataBlockBinarySearch
  data_block_hash_table_util_ratio=0.750000
  cache_index_and_filter_blocks=true
  block_restart_interval=16
  pin_l0_filter_and_index_blocks_in_cache=true
  hash_index_allow_collision=true
  cache_index_and_filter_blocks_with_high_priority=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  

[CFOptions "enumOneState"]
  bottommost_compression=kDisableCompressionOption
  sample_for_compression=0
  blob_garbage_collection_age_cutoff=0.250000
  arena_block_size=8388608
  enable_blob_garbage_collection=false
  level0_stop_writes_trigger=36
  min_blob_size=0
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;min_merge_width=2;compression_size_percent=-1;max_size_amplification_percent=200;max_merge_width=4294967295;size_ratio=1;}
  target_file_size_base=67108864
  max_bytes_for_level_base=268435456
  memtable_whole_key_filtering=false
  soft_pending_compaction_bytes_limit=68719476736
  blob_compression_type=kNoCompression
  max_write_buffer_number=2
  ttl=2592000
  compaction_options_fifo={allow_compaction=false;max_table_files_size=1073741824;}
  check_flush_compaction_key_order=true
  max_successive_merges=0
  inplace_update_num_locks=10000
  bottommost_compression_opts={enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  target_file_size_multiplier=1
  max_bytes_for_level_multiplier_additional=1:{1}:{1}:{1}:{1}:{1}:{1}
  enable_blob_files=false
  level0_slowdown_writes_trigger=20
  compression=kSnappyCompression
  level0_file_num_compaction_trigger=4
  blob_file_size=268435456
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier=10.000000
  write_buffer_size=67108864
  disable_auto_compactions=false
  max_compaction_bytes=1677721600
  memtable_huge_page_size=0
  compression_opts={enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  hard_pending_compaction_bytes_limit=274877906944
  periodic_compaction_seconds=0
  paranoid_file_checks=false
  memtable_prefix_bloom_size_ratio=0.000000
  max_sequential_skip_in_iterations=8
  report_bg_io_stats=false
  compaction_pri=kMinOverlappingRatio
  compaction_style=kCompactionStyleLevel
  memtable_factory=SkipListFactory
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  compaction_filter_factory=nullptr
  min_write_buffer_number_to_merge=1
  max_write_buffer_number_to_maintain=0
  compaction_filter=nullptr
  merge_operator=StringAppendTESTOperator
  num_levels=7
  optimize_filters_for_hits=false
  force_consistency_checks=true
  table_factory=BlockBasedTable
  max_write_buffer_size_to_maintain=0
  memtable_insert_with_hint_prefix_extractor=nullptr
  level_compaction_dynamic_level_bytes=false
  inplace_update_support=false
  
[TableOptions/BlockBasedTable "enumOneState"]
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  read_amp_bytes_per_bit=0
  verify_compression=false
  format_version=5
  optimize_filters_for_memory=false
  partition_filters=false
  max_auto_readahead_size=262144
  enable_index_compression=true
  checksum=kCRC32c
  index_block_restart_interval=1
  pin_top_level_index_and_filter=true
  block_align=false
  block_size=4096
  index_type=kBinarySearch
  filter_policy=nullptr
  metadata_block_size=4096
  no_block_cache=false
  whole_key_filtering=true
  index_shortening=kShortenSeparators
  block_size_deviation=10
  data_block_index_type=kDataBlockBinarySearch
  data_block_hash_table_util_ratio=0.750000
  cache_index_and_filter_blocks=true
  block_restart_interval=16
  pin_l0_filter_and_index_blocks_in_cache=true
  hash_index_allow_collision=true
  cache_index_and_filter_blocks_with_high_priority=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  

[CFOptions "enumThreeState"]
  bottommost_compression=kDisableCompressionOption
  sample_for_compression=0
  blob_garbage_collection_age_cutoff=0.250000
  arena_block_size=8388608
  enable_blob_garbage_collection=false
  level0_stop_writes_trigger=36
  min_blob_size=0
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;min_merge_width=2;compression_size_percent=-1;max_size_amplification_percent=200;max_merge_width=4294967295;size_ratio=1;}
  target_file_size_base=67108864
  max_bytes_for_level_base=268435456
  memtable_whole_key_filtering=false
  soft_pending_compaction_bytes_limit=68719476736
  blob_compression_type=kNoCompression
  max_write_buffer_number=2
  ttl=2592000
  compaction_options_fifo={allow_compaction=false;max_table_files_size=1073741824;}
  check_flush_compaction_key_order=true
  max_successive_merges=0
  inplace_update_num_locks=10000
  bottommost_compression_opts={enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  target_file_size_multiplier=1
  max_bytes_for_level_multiplier_additional=1:{1}:{1}:{1}:{1}:{1}:{1}
  enable_blob_files=false
  level0_slowdown_writes_trigger=20
  compression=kSnappyCompression
  level0_file_num_compaction_trigger=4
  blob_file_size=268435456
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier=10.000000
  write_buffer_size=67108864
  disable_auto_compactions=false
  max_compaction_bytes=1677721600
  memtable_huge_page_size=0
  compression_opts={enabled=false;parallel_threads=1;zstd_max_train_bytes=0;max_dict_bytes=0;strategy=0;max_dict_buffer_bytes=0;level=32767;window_bits=-14;}
  hard_pending_compaction_bytes_limit=274877906944
  periodic_compaction_seconds=0
  paranoid_file_checks=false
  memtable_prefix_bloom_size_ratio=0.000000
  max_sequential_skip_in_iterations=8
  report_bg_io_stats=false
  compaction_pri=kMinOverlappingRatio
  compaction_style=kCompactionStyleLevel
  memtable_factory=SkipListFactory
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  compaction_filter_factory=nullptr
  min_write_buffer_number_to_merge=1
  max_write_buffer_number_to_maintain=0
  compaction_filter=nullptr
  merge_operator=StringAppendTESTOperator
  num_levels=7
  optimize_filters_for_hits=false
  force_consistency_checks=true
  table_factory=BlockBasedTable
  max_write_buffer_size_to_maintain=0
  memtable_insert_with_hint_prefix_extractor=nullptr
  level_compaction_dynamic_level_bytes=false
  inplace_update_support=false
  
[TableOptions/BlockBasedTable "enumThreeState"]
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  read_amp_bytes_per_bit=0
  verify_compression=false
  format_version=5
  optimize_filters_for_memory=false
  partition_filters=false
  max_auto_readahead_size=262144
  enable_index_compression=true
  checksum=kCRC32c
  index_block_restart_interval=1
  pin_top_level_index_and_filter=true
  block_align=false
  block_size=4096
  index_type=kBinarySearch
  filter_policy=nullptr
  metadata_block_size=4096
  no_block_cache=false
  whole_key_filtering=true
  index_shortening=kShortenSeparators
  block_size_deviation=10
  data_block_index_type=kDataBlockBinarySearch
  data_block_hash_table_util_ratio=0.750000
  cache_index_and_filter_blocks=true
  block_restart_interval=16
  pin_l0_filter_and_index_blocks_in_cache=true
  hash_index_allow_collision=true
  cache_index_and_filter_blocks_with_high_priority=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  
