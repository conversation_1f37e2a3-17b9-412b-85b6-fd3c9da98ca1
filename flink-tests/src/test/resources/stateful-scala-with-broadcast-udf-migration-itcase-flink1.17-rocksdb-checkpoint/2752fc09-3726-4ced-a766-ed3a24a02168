# This is a RocksDB option file.
#
# For detailed file format spec, please refer to the example file
# in examples/rocksdb_option_file_example.ini
#

[Version]
  rocksdb_version=6.20.3
  options_file_version=1.1

[DBOptions]
  max_background_flushes=-1
  compaction_readahead_size=0
  wal_bytes_per_sync=0
  bytes_per_sync=0
  max_open_files=-1
  stats_history_buffer_size=1048576
  stats_dump_period_sec=0
  stats_persist_period_sec=600
  delete_obsolete_files_period_micros=21600000000
  max_total_wal_size=0
  strict_bytes_per_sync=false
  delayed_write_rate=16777216
  avoid_flush_during_shutdown=false
  writable_file_max_buffer_size=1048576
  max_subcompactions=1
  base_background_compactions=-1
  max_background_compactions=-1
  max_background_jobs=2
  allow_data_in_errors=false
  avoid_unnecessary_blocking_io=false
  log_readahead_size=0
  manual_wal_flush=false
  two_write_queues=false
  write_dbid_to_manifest=false
  allow_ingest_behind=false
  avoid_flush_during_recovery=false
  best_efforts_recovery=false
  info_log_level=INFO_LEVEL
  access_hint_on_compaction_start=NORMAL
  write_thread_max_yield_usec=100
  max_write_batch_group_size_bytes=1048576
  write_thread_slow_yield_usec=3
  wal_recovery_mode=kPointInTimeRecovery
  allow_concurrent_memtable_write=true
  db_host_id=__hostname__
  unordered_write=false
  enable_pipelined_write=false
  fail_if_options_file_error=false
  persist_stats_to_disk=false
  WAL_size_limit_MB=0
  bgerror_resume_retry_interval=1000000
  wal_dir=/tmp/junit8868211846952945563/junit5858729405336621757/minicluster_6ec4bc0f25be358d5d2b75e4a66b6014/tm_0/tmp/job_f935fcab2ef0f74772ad417bc6f76dfa_op_CoBroadcastWithKeyedOperator_c01997dd69890f74ab92174f0d6e3ca2__4_4__uuid_63e179db-2d32-46aa-a5ee-3fdebd4009cd/db
  log_file_time_to_roll=0
  keep_log_file_num=4
  WAL_ttl_seconds=0
  db_write_buffer_size=0
  table_cache_numshardbits=6
  max_file_opening_threads=16
  random_access_max_buffer_size=1048576
  max_bgerror_resume_count=2147483647
  skip_checking_sst_file_sizes_on_db_open=false
  skip_stats_update_on_db_open=false
  track_and_verify_wals_in_manifest=false
  dump_malloc_stats=false
  paranoid_checks=true
  is_fd_close_on_exec=true
  max_manifest_file_size=1073741824
  error_if_exists=false
  use_adaptive_mutex=false
  atomic_flush=false
  enable_thread_tracking=false
  create_missing_column_families=false
  create_if_missing=true
  manifest_preallocation_size=4194304
  use_fsync=false
  allow_2pc=false
  recycle_log_file_num=0
  use_direct_io_for_flush_and_compaction=false
  use_direct_reads=false
  allow_mmap_writes=false
  preserve_deletes=false
  enable_write_thread_adaptive_yield=true
  max_log_file_size=26214400
  allow_fallocate=true
  allow_mmap_reads=false
  new_table_reader_for_compaction_inputs=false
  advise_random_on_open=true
  

[CFOptions "default"]
  bottommost_compression=kDisableCompressionOption
  bottommost_compression_opts={max_dict_buffer_bytes=0;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;strategy=0;max_dict_bytes=0;level=32767;window_bits=-14;}
  sample_for_compression=0
  blob_file_size=268435456
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;compression_size_percent=-1;max_size_amplification_percent=200;max_merge_width=4294967295;min_merge_width=2;size_ratio=1;}
  compaction_options_fifo={allow_compaction=false;max_table_files_size=1073741824;}
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier_additional=1:{1}:{1}:{1}:{1}:{1}:{1}
  max_bytes_for_level_base=268435456
  memtable_whole_key_filtering=false
  memtable_prefix_bloom_size_ratio=0,000000
  enable_blob_files=false
  target_file_size_base=67108864
  memtable_huge_page_size=0
  max_successive_merges=0
  inplace_update_num_locks=10000
  max_sequential_skip_in_iterations=8
  arena_block_size=8388608
  target_file_size_multiplier=1
  max_write_buffer_number=2
  write_buffer_size=67108864
  blob_compression_type=kNoCompression
  compression=kSnappyCompression
  level0_stop_writes_trigger=36
  level0_slowdown_writes_trigger=20
  level0_file_num_compaction_trigger=4
  ttl=2592000
  max_compaction_bytes=1677721600
  blob_garbage_collection_age_cutoff=0,250000
  compression_opts={max_dict_buffer_bytes=0;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;strategy=0;max_dict_bytes=0;level=32767;window_bits=-14;}
  enable_blob_garbage_collection=false
  soft_pending_compaction_bytes_limit=68719476736
  paranoid_file_checks=false
  periodic_compaction_seconds=0
  check_flush_compaction_key_order=true
  min_blob_size=0
  hard_pending_compaction_bytes_limit=274877906944
  disable_auto_compactions=false
  max_bytes_for_level_multiplier=10,000000
  report_bg_io_stats=false
  compaction_pri=kMinOverlappingRatio
  compaction_filter_factory=nullptr
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  compaction_style=kCompactionStyleLevel
  min_write_buffer_number_to_merge=1
  max_write_buffer_size_to_maintain=0
  max_write_buffer_number_to_maintain=0
  merge_operator=StringAppendTESTOperator
  memtable_factory=SkipListFactory
  memtable_insert_with_hint_prefix_extractor=nullptr
  num_levels=7
  force_consistency_checks=true
  optimize_filters_for_hits=false
  compaction_filter=nullptr
  level_compaction_dynamic_level_bytes=false
  inplace_update_support=false
  table_factory=BlockBasedTable
  
[TableOptions/BlockBasedTable "default"]
  pin_top_level_index_and_filter=true
  block_align=false
  read_amp_bytes_per_bit=0
  verify_compression=false
  enable_index_compression=true
  whole_key_filtering=true
  max_auto_readahead_size=262144
  optimize_filters_for_memory=false
  index_block_restart_interval=1
  block_restart_interval=16
  block_size=4096
  format_version=5
  partition_filters=false
  block_size_deviation=10
  no_block_cache=false
  checksum=kCRC32c
  data_block_hash_table_util_ratio=0,750000
  index_shortening=kShortenSeparators
  data_block_index_type=kDataBlockBinarySearch
  hash_index_allow_collision=true
  filter_policy=nullptr
  metadata_block_size=4096
  index_type=kBinarySearch
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  pin_l0_filter_and_index_blocks_in_cache=true
  cache_index_and_filter_blocks_with_high_priority=true
  cache_index_and_filter_blocks=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  

[CFOptions "_timer_state/processing_user-timers"]
  bottommost_compression=kDisableCompressionOption
  bottommost_compression_opts={max_dict_buffer_bytes=0;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;strategy=0;max_dict_bytes=0;level=32767;window_bits=-14;}
  sample_for_compression=0
  blob_file_size=268435456
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;compression_size_percent=-1;max_size_amplification_percent=200;max_merge_width=4294967295;min_merge_width=2;size_ratio=1;}
  compaction_options_fifo={allow_compaction=false;max_table_files_size=1073741824;}
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier_additional=1:{1}:{1}:{1}:{1}:{1}:{1}
  max_bytes_for_level_base=268435456
  memtable_whole_key_filtering=false
  memtable_prefix_bloom_size_ratio=0,000000
  enable_blob_files=false
  target_file_size_base=67108864
  memtable_huge_page_size=0
  max_successive_merges=0
  inplace_update_num_locks=10000
  max_sequential_skip_in_iterations=8
  arena_block_size=8388608
  target_file_size_multiplier=1
  max_write_buffer_number=2
  write_buffer_size=67108864
  blob_compression_type=kNoCompression
  compression=kSnappyCompression
  level0_stop_writes_trigger=36
  level0_slowdown_writes_trigger=20
  level0_file_num_compaction_trigger=4
  ttl=2592000
  max_compaction_bytes=1677721600
  blob_garbage_collection_age_cutoff=0,250000
  compression_opts={max_dict_buffer_bytes=0;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;strategy=0;max_dict_bytes=0;level=32767;window_bits=-14;}
  enable_blob_garbage_collection=false
  soft_pending_compaction_bytes_limit=68719476736
  paranoid_file_checks=false
  periodic_compaction_seconds=0
  check_flush_compaction_key_order=true
  min_blob_size=0
  hard_pending_compaction_bytes_limit=274877906944
  disable_auto_compactions=false
  max_bytes_for_level_multiplier=10,000000
  report_bg_io_stats=false
  compaction_pri=kMinOverlappingRatio
  compaction_filter_factory=nullptr
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  compaction_style=kCompactionStyleLevel
  min_write_buffer_number_to_merge=1
  max_write_buffer_size_to_maintain=0
  max_write_buffer_number_to_maintain=0
  merge_operator=StringAppendTESTOperator
  memtable_factory=SkipListFactory
  memtable_insert_with_hint_prefix_extractor=nullptr
  num_levels=7
  force_consistency_checks=true
  optimize_filters_for_hits=false
  compaction_filter=nullptr
  level_compaction_dynamic_level_bytes=false
  inplace_update_support=false
  table_factory=BlockBasedTable
  
[TableOptions/BlockBasedTable "_timer_state/processing_user-timers"]
  pin_top_level_index_and_filter=true
  block_align=false
  read_amp_bytes_per_bit=0
  verify_compression=false
  enable_index_compression=true
  whole_key_filtering=true
  max_auto_readahead_size=262144
  optimize_filters_for_memory=false
  index_block_restart_interval=1
  block_restart_interval=16
  block_size=4096
  format_version=5
  partition_filters=false
  block_size_deviation=10
  no_block_cache=false
  checksum=kCRC32c
  data_block_hash_table_util_ratio=0,750000
  index_shortening=kShortenSeparators
  data_block_index_type=kDataBlockBinarySearch
  hash_index_allow_collision=true
  filter_policy=nullptr
  metadata_block_size=4096
  index_type=kBinarySearch
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  pin_l0_filter_and_index_blocks_in_cache=true
  cache_index_and_filter_blocks_with_high_priority=true
  cache_index_and_filter_blocks=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  

[CFOptions "_timer_state/event_user-timers"]
  bottommost_compression=kDisableCompressionOption
  bottommost_compression_opts={max_dict_buffer_bytes=0;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;strategy=0;max_dict_bytes=0;level=32767;window_bits=-14;}
  sample_for_compression=0
  blob_file_size=268435456
  compaction_options_universal={allow_trivial_move=false;stop_style=kCompactionStopStyleTotalSize;compression_size_percent=-1;max_size_amplification_percent=200;max_merge_width=4294967295;min_merge_width=2;size_ratio=1;}
  compaction_options_fifo={allow_compaction=false;max_table_files_size=1073741824;}
  prefix_extractor=nullptr
  max_bytes_for_level_multiplier_additional=1:{1}:{1}:{1}:{1}:{1}:{1}
  max_bytes_for_level_base=268435456
  memtable_whole_key_filtering=false
  memtable_prefix_bloom_size_ratio=0,000000
  enable_blob_files=false
  target_file_size_base=67108864
  memtable_huge_page_size=0
  max_successive_merges=0
  inplace_update_num_locks=10000
  max_sequential_skip_in_iterations=8
  arena_block_size=8388608
  target_file_size_multiplier=1
  max_write_buffer_number=2
  write_buffer_size=67108864
  blob_compression_type=kNoCompression
  compression=kSnappyCompression
  level0_stop_writes_trigger=36
  level0_slowdown_writes_trigger=20
  level0_file_num_compaction_trigger=4
  ttl=2592000
  max_compaction_bytes=1677721600
  blob_garbage_collection_age_cutoff=0,250000
  compression_opts={max_dict_buffer_bytes=0;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;strategy=0;max_dict_bytes=0;level=32767;window_bits=-14;}
  enable_blob_garbage_collection=false
  soft_pending_compaction_bytes_limit=68719476736
  paranoid_file_checks=false
  periodic_compaction_seconds=0
  check_flush_compaction_key_order=true
  min_blob_size=0
  hard_pending_compaction_bytes_limit=274877906944
  disable_auto_compactions=false
  max_bytes_for_level_multiplier=10,000000
  report_bg_io_stats=false
  compaction_pri=kMinOverlappingRatio
  compaction_filter_factory=nullptr
  comparator=leveldb.BytewiseComparator
  bloom_locality=0
  compaction_style=kCompactionStyleLevel
  min_write_buffer_number_to_merge=1
  max_write_buffer_size_to_maintain=0
  max_write_buffer_number_to_maintain=0
  merge_operator=StringAppendTESTOperator
  memtable_factory=SkipListFactory
  memtable_insert_with_hint_prefix_extractor=nullptr
  num_levels=7
  force_consistency_checks=true
  optimize_filters_for_hits=false
  compaction_filter=nullptr
  level_compaction_dynamic_level_bytes=false
  inplace_update_support=false
  table_factory=BlockBasedTable
  
[TableOptions/BlockBasedTable "_timer_state/event_user-timers"]
  pin_top_level_index_and_filter=true
  block_align=false
  read_amp_bytes_per_bit=0
  verify_compression=false
  enable_index_compression=true
  whole_key_filtering=true
  max_auto_readahead_size=262144
  optimize_filters_for_memory=false
  index_block_restart_interval=1
  block_restart_interval=16
  block_size=4096
  format_version=5
  partition_filters=false
  block_size_deviation=10
  no_block_cache=false
  checksum=kCRC32c
  data_block_hash_table_util_ratio=0,750000
  index_shortening=kShortenSeparators
  data_block_index_type=kDataBlockBinarySearch
  hash_index_allow_collision=true
  filter_policy=nullptr
  metadata_block_size=4096
  index_type=kBinarySearch
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  pin_l0_filter_and_index_blocks_in_cache=true
  cache_index_and_filter_blocks_with_high_priority=true
  cache_index_and_filter_blocks=true
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  
